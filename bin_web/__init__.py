# -*- coding: utf-8 -*-
"""
Created on 2024/8/15 上午11:12

@Project -> File: srv-pollution-jiulongpo-emission -> __init__.py.py

@Author: chen<PERSON>

@Describe:
"""
import os
import sys
import time
import shutil
import traceback
from fastapi import FastAPI, Query, Response, HTTPException, BackgroundTasks, status
from starlette.middleware.cors import CORSMiddleware
from fastapi import applications
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.staticfiles import StaticFiles
from fastapi.requests import Request
from contextlib import asynccontextmanager

sys.path.append('../')
from lib import proj_dir, logger


# @asynccontextmanager
# async def lifespan(app: FastAPI):
#     # 启动时执行一次
#     logger.info('启动时执行清空裁量标志文件存储目录')
#     if os.path.exists(punish_task_dir):
#         shutil.rmtree(punish_task_dir)
#     os.makedirs(punish_task_dir, exist_ok=True)
#     yield
#     # 关闭时执行清理（可选）
#     logger.info('服务退出')


def swagger_monkey_patch(*args, **kwargs):
    """
	Wrap the function which is generating the HTML for the /docs endpoint and
	overwrite the default values for the swagger js and css.
	"""
    return get_swagger_ui_html(
        *args, **kwargs,
        swagger_js_url = "static/ui/swagger-ui-bundle.js",
        swagger_css_url = "static/ui/swagger-ui.css",
        swagger_favicon_url = "static/ui/favicon-32x32.png"
    )


def redoc_monkey_patch(*args, **kwargs):
    """
	Wrap the function which is generating the HTML for the /docs endpoint and
	overwrite the default values for the swagger js and css.
	"""
    return get_redoc_html(
        *args, **kwargs,
        redoc_js_url = "static/redoc/redoc.standalone.js",
        redoc_favicon_url = "static/redoc/bundles/favicon.png",
    )


applications.get_swagger_ui_html = swagger_monkey_patch
applications.get_redoc_html = redoc_monkey_patch

# 创建一个FastApi实例
# app = FastAPI(lifespan=lifespan)
app = FastAPI()

app.mount('/static', StaticFiles(directory = f'{proj_dir}/static'), name = 'static')

# 设置允许访问的域名
origins = ["*"]  # 也可以设置为"*"，即为所有。

# 设置跨域传参
app.add_middleware(
    CORSMiddleware,
    allow_origins = origins,  # 设置允许的origins来源
    allow_credentials = True,
    allow_methods = ["*"],  # 设置允许跨域的http方法，比如 get、post、put等。
    allow_headers = ["*"])  # 允许跨域的headers，可以用来鉴别来源等作用


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """ # 添加中间件: 计算每次接口请求的响应时间; 接口请求异常捕获，并 """
    try:
        start_time = time.time()
        responser = await call_next(request)
        aut_token = request.headers.get('X-token', None)

        # base64.b64encode('rockontrol_air_quality_machine_learning'.encode('utf-8'))
        if request.method == 'OPTIONS':
            return Response(headers = {"Access-Control-Allow-Methods": "PUT,GET,POST,DELETE,OPTIONS",
                                       "Access-Control-Allow-Origin": "*",
                                       "Access-Control-Allow-Headers": "*",
                                       "Access-Control-Expose-Headers": "*",
                                       "Access-Control-Allow-Credentials": "true"},
                            content = "ok", status_code = 200)
        else:
            if request.url.path != '/ai_law/get_token/' and  \
                    aut_token != 'cm9ja29udHJvbF9haXJfcXVhbGl0eV9tYWNoaW5lX2xlYXJuaW5n':
                logger.info(f"user not unauthorized to route {request.url.path}")
                return Response(content = "no access", status_code = 401)

        process_time = round(time.time() - start_time, 4)
        # 返回接口响应时间
        responser.headers["X-Process-Time"] = f"{process_time} (s)"
        return responser

    except Exception as _:
        logger.error(f" error request: {request.url.path} \n {traceback.format_exc()}")
        raise HTTPException(status_code = 500, detail = {"message": f"{request.url.path} \
			接口请求异常，联系工程师修复."})
