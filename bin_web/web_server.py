# -*- coding: utf-8 -*-
"""
Created on 2024/8/15 上午11:14

@ Date: 2025-07-17 20:07:38 PM

@ FilePath: -> srv-enterprise-power-usage-verify -> bin_web -> web_server.py

@Describe:
"""
import json
import os
import sys
import traceback
import uvicorn

sys.path.append('../')
from lib import (logger, city_code_province_pinyin_map, city_code_pinyin_map,
                 cached_dir
                 )
from bin_web import app, Query
from lib.step_0_check_power.step_0_verify_power_usage import get_gov_facility_anomalies
from lib.step_0_check_power.data_utils import get_ue_info_and_data
from lib.web_utils.api_utils import *
from mod.tool.s3_check_md5 import get_md5_from_bytesOrStr
from mod.tool.dir_file_op import save_json_file, MyEncoder, load_json_file

time_format = f'%Y-%m-%d %H:%M:%S'


@app.get('/ue/liveness/', description = '健康检查')
async def check_liveness():
    return {'status_code': 200, 'data': 'OK'}


@app.post('/ue/check/', description = '校验各个企业的监测数据是否符合监测方案')
async def exec_verification(
        area_code: str = Query(description = f"9位数字区域编码，通常精确到地市级"),
        task_id: str = Query(None, description = f"任务编码"),
        start_time: str = Query(description = f"校验起始时间，默认为, "
                                                  f"`%Y-%m-%d %H:%M:%S` 或 `%Y-%m-%d %H:%M` "
                                                  f"或 `%Y-%m-%d %H` 或 `%Y-%m-%d`"),
        end_time: str = Query(datetime.datetime.now().strftime(time_format),
                              description = "校验结束时间，默认为当前时刻, "
                                                f"`%Y-%m-%d %H:%M:%S` 或 `%Y-%m-%d %H:%M` "
                                                f"或 `%Y-%m-%d %H` 或 `%Y-%m-%d`"),
        force_exec: bool = Query(False, description = "是否强制执行重新计算，默认false，"
                                                      "即有缓存文件时，优先加载缓存")
):
    callback_result = {
        'status_code': 200,
        'task_id': '',
        'area_code': area_code,
        'date_range': [],
        'message': '',
        'data': {},
    }
    try:
        params = dict(start_date = start_time, end_date = end_time,
                      area_code = area_code, force_exec = force_exec,
                      task_id = task_id)
        logger.info(f'>>>>>>>>>>>>>>> start new verification analyzing, parameters: {params}')

        # ====== 参数检查，设置默认参数 ======
        start_calc = datetime.datetime.now()
        now_time = start_calc.strftime(time_format)
        start_time = start_time.strip()
        end_time = end_time.strip() or now_time
        start_time = ckeck_date_format(start_time)
        end_time = ckeck_date_format(end_time)
        assert start_time < end_time, f'校验起始时间不得早于结束时间, 但传入时间为 `{start_time}` vs {end_time}'
        assert end_time <= now_time, \
            f'校验结束时间不得早于当前时间 `{now_time}`, 但传入时间为 `{end_time}`'

        province_pinyin = city_code_province_pinyin_map.get(area_code)
        assert province_pinyin, f'传入区域编码不合法，无法找到对应省份名 -> {area_code}'
        city_name = city_code_pinyin_map.get(area_code, province_pinyin)

        # 10:精确到天， 13:精确到小时, 16:精确到分钟
        prec = 10
        time_flag = f"{start_time[:prec].replace('-', '')}_{end_time[:prec].replace('-', '')}"
        file_tag = f'{area_code}_{time_flag}'
        if not task_id:
            task_id = get_md5_from_bytesOrStr(file_tag)
        file_name_template = f'{province_pinyin}_{city_name}_{area_code}_{task_id}_' + '{name}' + f'_{time_flag}.' + '{suffix}'
        file_name = file_name_template.format(name = 'abnormal', suffix = 'json')
        file_dir = f'{cached_dir}/ue/{province_pinyin}_{city_name}_{task_id}_{time_flag}/'
        os.makedirs(file_dir, exist_ok = True)
        file_path = f'{file_dir}/{file_name}'

        if not force_exec and os.path.isfile(file_path):
            try:
                callback_result = load_json_file(file_path)
                logger.info(f'>>>>>>>>>>>>>> load cached {file_name} successfully')
                return json.loads(json.dumps(callback_result, cls = MyEncoder))
            except Exception as e:
                logger.error(f'==> load cached file error, recomputing the result -> {e}')

        clues = {}
        df_info, df_data = get_ue_info_and_data(province_pinyin, area_code, start_time, end_time)
        clues['gov_facility_anomaly'] = get_gov_facility_anomalies(df_info, df_data, start_time, end_time, file_dir)

        callback_result['task_id'] = task_id
        callback_result['data'] = clues
        callback_result['date_range'] = [start_time, end_time]
        callback_result['message'] = ''
        callback_result = json.loads(json.dumps(callback_result, cls = MyEncoder))
        save_json_file(callback_result, file_path, ensure_ascii = False)
        logger.info(f'>>>>>>>>>>>>>> calculating `/check/` task {task_id} successfully, '
                    f'time cost: {(datetime.datetime.now() - start_calc).seconds}')

    except Exception as e:
        callback_result['status_code'] = 400
        callback_result['message'] = str(e)
        logger.error(f'>>>>>>>>>>>>>>> calculation error -> {traceback.print_exc()}')
    # return json.loads(json.dumps(callback_result, cls = MyEncoder))
    return callback_result


if __name__ == '__main__':
    uvicorn.run(app = 'web_server:app', port = 8080, host = "0.0.0.0", workers = 2)
