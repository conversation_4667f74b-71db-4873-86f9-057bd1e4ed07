# -*- coding: utf-8 -*-
"""
@File  : logObj.py
@Author: <PERSON>
@Date  : 2021/8/10 10:39
@Desc  : 
"""

import traceback
import logging
from logging import handlers
import os
import pathlib
import threading


class Logger(object):
    # 日志线程安全锁
    _instance_lock = threading.Lock()

    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'critical': logging.CRITICAL
    }

    def __new__(cls, *args, **kwargs):
        if not hasattr(Logger, "_instance"):
            with Logger._instance_lock:
                if not hasattr(Logger, "_instance"):
                    Logger._instance = super().__new__(cls)
                    cls.init(*args, **kwargs)
        return Logger._instance

    @classmethod
    def init(cls, path, level=None, when=None, backCount=None, fmt=None):
        _proj_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
        full_path = os.path.join(_proj_dir, path)
        if not os.path.isdir(full_path):
            os.makedirs(full_path)
        if level is None:
            level = 'info'
        if when is None:
            when = 'd'
        if backCount is None:
            backCount = 5
        if fmt is None:
            fmt = '%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'

        filename = full_path + level + '.log'
        # pathlib.Path(filename).touch()
        format_str = logging.Formatter(fmt)

        cls.logger = logging.getLogger("logger")
        cls.logger.setLevel(cls.level_relations.get(level))
        cls.logger.propagate = 0        # 防止消息传播到root记录器

        sh = logging.StreamHandler()
        sh.setLevel(cls.level_relations.get(level))
        sh.setFormatter(format_str)
        th = handlers.TimedRotatingFileHandler(filename=filename, when=when, backupCount=backCount, encoding='utf-8')
        th.setLevel(cls.level_relations.get(level))
        th.setFormatter(format_str)

        cls.logger.addHandler(sh)
        cls.logger.addHandler(th)


if __name__ == '__main__':
    
    logger = Logger('../data/logs/surround/', 'info',
                     fmt="%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s").logger
    logger.info('start')
    logger.warning('warning')
