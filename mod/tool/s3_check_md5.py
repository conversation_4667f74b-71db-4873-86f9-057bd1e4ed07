#!/usr/bin/python
# -*- coding: utf-8 -*-

"""

<AUTHOR> <PERSON><PERSON>@rkcl, vincent-cj

@Email : <EMAIL>

@Time : 3/8/23 6:20 PM

@Project : srv-wrfcmaq-forecast-post-pingxiang-online -> s3_check_md5

Description : 

"""
from typing import List, Union
import hashlib


def get_md5_from_bytesOrStr(data: Union[str, bytes, List[Union[str, bytes]]]):
    """根据二进制流获取md5校验码"""
    if isinstance(data, (str, bytes)):
        data = [data]
    assert isinstance(data, list), f'传入数据格式应为二进制、字符串或包含二者的列表，但得到的数据格式为 {type(data)}'

    m = hashlib.md5()
    for d in data:
        if isinstance(d, str):
            d = d.encode(encoding='utf-8')
        assert isinstance(d, bytes), f'传入数据元素的格式应为二进制或字符串，但得到的数据格式为 {type(d)}'
        m.update(d)
    return m.hexdigest()


def md5_checksum(filename):
    m = hashlib.md5()
    with open(filename, 'rb') as f:
        for data in iter(lambda: f.read(1024 * 1024), b''):
            m.update(data)

    return m.hexdigest()


def etag_checksum(filename, chunk_size=8 * 1024 * 1024):
    md5s = []
    with open(filename, 'rb') as f:
        for data in iter(lambda: f.read(chunk_size), b''):
            md5s.append(hashlib.md5(data).digest())
    m = hashlib.md5(b"".join(md5s))
    # print('{}-{}'.format(m.hexdigest(), len(md5s)))
    return '{}-{}'.format(m.hexdigest(), len(md5s))


def etag_compare(filename, etag):
    et = etag[1:-1]  # strip quotes
    # print('et', et)
    if '-' in et and et == etag_checksum(filename):
        return True
    if '-' not in et and et == md5_checksum(filename):
        return True
    return False


def main():
    import boto3
    session = boto3.Session(
        aws_access_key_id='s3_accesskey',
        aws_secret_access_key='s3_secret'
    )
    s3 = session.client('s3')
    obj_dict = s3.get_object(Bucket='bucket_name', Key='your_key')

    etag = (obj_dict['ETag'])
    print('etag', etag)

    validation = etag_compare('filename', etag)
    print(validation)
    etag_checksum('filename', chunk_size=8 * 1024 * 1024)
    return validation
