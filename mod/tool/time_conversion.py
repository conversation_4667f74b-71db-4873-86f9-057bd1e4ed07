# -*- coding: utf-8 -*-
"""
Created on 2020/12/21 下午4:10

@Project -> time_conversion.py

@Author: <PERSON><PERSON><PERSON>, wa<PERSON><PERSON><PERSON><PERSON><PERSON>

@Describe: 时间转换
"""

import time
import datetime
import numpy as np
import pandas as pd

t_init = (1970, 1, 1, 8, 0, 0, 3, 1, 0)  # 时间戳的起始记录时间，防止因时区不同导致计算出错


def strf2utc(t, in_format = '%Y-%m-%d %H:%M:%S', out_format = '%Y-%m-%dT%H:%M:%S+08:00'):
    """
    将北京时间字符串转换为UTC时间
    :param t: str like "%Y-%m-%d %H:%M:%S", 当地时间
    :param in_format: 需要转换的原格式
    :param out_format: 需要转成的格式
    :return: t_utc: str like "%Y-%m-%dT%H:%M:%S+08:00", 转换后的UTC时间
    """
    t_date = time.strptime(t, in_format)
    stp = int(time.mktime(t_date) - int(time.mktime(t_init)))
    t_utc = time.strftime(out_format, time.localtime(stp))
    return t_utc


def strf2stp(t, time_format = '%Y-%m-%dT%H:%M:%S+08:00'):
    """时间字符串转为时间戳"""
    stp = int(time.mktime(time.strptime(t, time_format)) - time.mktime(t_init))
    return stp


def utc2stp(utc):
    """
    将UTC时间转为时间戳
    :param utc: str like "%Y-%m-%dT%H:%M:%S+08:00", UTC时间
    :return: stp: int, 转换后的时间戳
    """
    t_date = time.strptime(utc, '%Y-%m-%dT%H:%M:%S+08:00')
    stp = int(time.mktime(t_date) - time.mktime(t_init))
    return stp


def stp2strp(stp, time_format = '%Y-%m-%dT%H:%M:%S+08:00'):
    """时间戳转换为时间格式"""
    t_arr = time.localtime(stp - int(time.mktime(t_init)))
    t = time.strftime(time_format, t_arr)
    return t


def stp2strf(stp, f = '%Y-%m-%dT%H:%M:%S+08:00'):
    """将时间戳转为指定格式的字符串"""
    stp = int(stp) - time.mktime(t_init)
    return time.strftime(f, time.localtime(stp))


def stp2MWH(stp, return_str = False):
    """从时间戳提取年/星期/小时字段"""
    t_struc: time.struct_time = time.localtime(stp - int(time.mktime(t_init)))
    if return_str is True:
        return f'[{t_struc.tm_mon}, {t_struc.tm_wday}, {t_struc.tm_hour}]'
    else:
        return t_struc.tm_mon, t_struc.tm_wday, t_struc.tm_hour


def stp2month(stp):
    """从时间戳提取月份"""
    month, weedday, hour = stp2MWH(stp, return_str = False)
    return month


def stp2week(stp):
    """从时间戳提取星期几"""
    month, weedday, hour = stp2MWH(stp, return_str = False)
    return weedday


def stp2week(stp):
    """从时间戳提取小时"""
    month, weedday, hour = stp2MWH(stp, return_str = False)
    return hour


def stp2ptime(stp):
    """将时间戳转为datetime格式的时间"""
    # ptime = datetime.datetime.fromtimestamp(int(stp - datetime.datetime(*t_init[:6]).timestamp()))
    ptime = datetime.datetime.fromtimestamp(int(stp - dt2stp(datetime.datetime(*t_init[:6]))))
    return ptime


def time2stp(t, time_format='%Y-%m-%dT%H:%M:%S+08:00'):
    """时间转为时间戳"""
    stp = int(time.mktime(time.strptime(t, time_format)) - time.mktime(t_init))
    return stp


def dt2stp(t: datetime.datetime,  time_format='%Y-%m-%dT%H:%M:%S+08:00'):
    stp = time2stp(t.strftime(time_format), time_format)
    return stp


def get_current_hour(current_stp: int):
    """获取当前小时数"""
    t = time.localtime(current_stp + int(time.mktime(t_init)))
    hour = t.tm_hour  # 当前小时
    return hour


def get_current_stp() -> float:
    """获取当前时间戳"""
    stp_current = time.time() - time.mktime(t_init)
    return stp_current


def digit_time2stp(x):
    x = str(x)
    year = x[0:4]
    month = x[4:6]
    day = x[6:8]
    hour = x[8:10]
    utc_time = year + '-' + month + '-' + day + 'T' + hour + ':00:00+08:00'
    return utc2stp(utc_time)


def get_curr_pred_stp(stp, shift, return_str = False):
    """
    获取实施cmaq预测的时刻, 即某天的23:00
    :param stp: 预测时刻
    :param shift: 实施预测的时刻与预测时刻相差的天数, 进一法圆整, 例如相差5小时, shift向上圆整为1
    :param return_str: 是否返回字符串, False时直接返回结果
    :return curr_time: 实施预测的时刻
            delta_stp: 实施预测的时刻与预测时刻相差的小时数
    """
    ptime = stp2ptime(stp)
    delta_ptime = datetime.timedelta(days = shift - 1, hours = ptime.hour + 1)
    curr_ptime = ptime - delta_ptime
    curr_time = int(curr_ptime.timestamp())
    delta_stp = delta_ptime.seconds // 3600 + delta_ptime.days * 24  # predict which step in the future.
    
    if return_str is True:
        return f'[{curr_time}, {delta_stp}]'
    else:
        return curr_time, delta_stp


def stp2YMWH(stp):
    """

    :param stp:
    :return:
    """
    t_arr: time.struct_time = time.localtime(stp + int(time.mktime(t_init)))
    return t_arr.tm_year, t_arr.tm_mon, t_arr.tm_mday, t_arr.tm_hour


def time_2_datetime(time):
    """

    :param time:
    :return:
    """
    return datetime.datetime(*stp2MWH(time), 0, 0)


def cal_full_time_series(start_time, end_time):
    """
    计算完整时间序列，10位数整型时间戳
    :return:
    """
    all_stps = np.arange(start_time, end_time+3600, 3600)
    time_range = pd.DataFrame({'time': all_stps})

    return time_range


def time_series_interpolate(df_data, length, time_range, targets):
    """
    时间序列插值
    :return:
    """
    df_data = df_data.drop_duplicates(subset='time', ignore_index=True)

    # 补全时间序列
    if df_data.shape[0] < length:
        df_data = time_range.merge(df_data, on='time', how='left')
    
    # 所有空位插值
    df_data[targets] = df_data[targets].apply(lambda x: x.interpolate(method='linear', limit_direction='both'))
    df_data = df_data.pad()   # 主要对grid_label补全，同时对其他潜在的异常缺失值补全，同时也保持通用性

    return df_data


def cal_time_range(Type, start_time, end_time):
    """
    根据不同的报告类型计算所需要的时间段，如: 环比时间、同比时间
    Args:
        start_time:
        end_time:
        Type: week、month、quarter、year

    Returns:
    """
    Type = Type.lower()
    if Type == 'week':
        # 环比时间
        delta_t = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
        last_week_s = str(delta_t - datetime.timedelta(days=7))
        last_week_e = str(delta_t - datetime.timedelta(hours=1))
        # 同比时间
        current_year = str(start_time)[:4]
        last_year = int(current_year) - 1
        last_year_current_week_start = str(last_year) + str(start_time)[4:]
        last_year_current_week_end = str(last_year) + str(end_time)[4:]
        return last_week_s, last_week_e, last_year_current_week_start, last_year_current_week_end
    elif Type == 'month':
        # 环比时间
        current_month = str(start_time)[:7]
        etime = datetime.datetime.strptime(current_month + '-01', '%Y-%m-%d')
        stime = etime - datetime.timedelta(days=1)
        last_month = str(stime)[:7]
        last_month_start = last_month + '-01 00:00:00'
        last_month_days = pd.Period(last_month_start, freq='M').days_in_month
        last_month_end = last_month + f'-{last_month_days} 23:00:00'
        # 同比时间
        last_year_current_month = f"{str(int(current_month[:4]) - 1)}{current_month[4:]}"
        last_year_current_month_start = last_year_current_month + '-01 00:00:00'
        last_year_current_month_days = pd.Period(last_year_current_month_start, freq='M').days_in_month
        last_year_current_month_end = last_year_current_month + f'-{last_year_current_month_days} 23:00:00'
        return last_month_start, last_month_end, last_year_current_month_start, last_year_current_month_end
    elif Type == 'quarter':
        # 环比时间
        current_month = str(start_time)[:7]
        etime = datetime.datetime.strptime(current_month + '-01', '%Y-%m-%d')
        stime = etime - datetime.timedelta(days=1)
        last_quarter_start_month = int(str(stime).split('-')[1]) - 2
        last_quarter_start = f'{str(stime)[:4]}-{last_quarter_start_month:02d}-01 00:00:00'
        last_quarter_end = str(stime).split(' ')[0] + ' 23:00:00'
        # 同比时间
        current_year = str(start_time)[:4]
        last_year = int(current_year) - 1
        last_year_current_quarter_start = str(last_year) + str(start_time)[4:]
        last_year_current_quarter_end = str(last_year) + str(end_time)[4:]
        return last_quarter_start, last_quarter_end, last_year_current_quarter_start, last_year_current_quarter_end
    elif Type == 'year':
        last_year = str(int(str(start_time)[:4])-1)
        last_year_start = last_year + '-01 00:00:00'
        last_year_month_days = pd.Period(last_year_start, freq='M').days_in_month
        last_year_end = last_year + f'-{last_year_month_days} 23:00:00'
        return last_year_start, last_year_end, last_year_start, last_year_end
    else:
        return [None, ]*4


def convert_time_strfstp(time_str):
    """将字符串格式的时间转为毫秒级时间戳"""
    # 定义时间字符串和格式
    time_format = '%Y-%m-%d %H:%M:%S'

    # 将字符串解析为本地时间（无时区信息）
    local_time = datetime.datetime.strptime(time_str, time_format)

    # 设置为东八区时区（UTC+8）
    east_8_timezone = datetime.timezone(datetime.timedelta(hours = 8))
    east_8_time = local_time.replace(tzinfo = east_8_timezone)

    # 转换为毫秒级时间戳
    timestamp_ms = int(east_8_time.timestamp() * 1000)
    return timestamp_ms


def convert_time_stf2strf(timestamp_ms):
    """将毫秒级时间戳转为时间字符串"""
    # 转换为秒（Python 的 timestamp 是秒级浮点数）
    timestamp_sec = timestamp_ms / 1000

    # 转换为 UTC+8 时间
    east_8_timezone = datetime.timezone(datetime.timedelta(hours = 8))
    utc8_time = datetime.datetime.fromtimestamp(timestamp_sec, east_8_timezone)

    # 格式化为字符串
    time_str = utc8_time.strftime('%Y年%m月%d日%H时')
    return time_str


if __name__ == "__main__":
    # cal_full_time_series(start_time=1614308400, end_time=1614564000)

    # res = cal_time_range(Type='week', start_time='2023-10-16 00:00:00', end_time='2023-10-22 23:00')
    # res = cal_time_range(Type='month', start_time='2023-10-01 00:00:00', end_time='2023-10-31 23:00')
    # res = cal_time_range(Type='quarter', start_time='2023-01-01 00:00:00', end_time='2023-03-31 23:00')
    res = cal_time_range(Type='year', start_time='2023-01-01 00:00:00', end_time='2023-12-31 23:00')

    last_start_time, last_end_time, last_year_start_time, last_year_end_time = res
