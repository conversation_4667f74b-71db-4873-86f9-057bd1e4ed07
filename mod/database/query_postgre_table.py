# -*- coding: utf-8 -*-
"""
Created on 2024/07/31 下午15:10

@Project -> File: srv-pollution-jiulongpo-emission > query_postgre_table.py

@Author: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>

@Email:

@Describe: 查询postgre数据库中的表
"""

import psycopg2
import pandas as pd


def query_postgre_table(config, sql):
    """
    查询postgre数据库中的表
    :param config:
    :param sql:
    :return:
    """
    # 创建连接
    conn = psycopg2.connect(**config)

    # 创建游标对象
    cur = conn.cursor()

    # 执行SQL查询
    cur.execute(sql)

    # 获取查询结果
    rows = cur.fetchall()

    # 获取表头信息
    table_header = [desc[0] for desc in cur.description]
    data_pd = pd.DataFrame(rows, columns=table_header)

    # 关闭游标和连接
    cur.close()
    conn.close()

    return data_pd


if __name__ == '__main__':
    import sys
    sys.path.append('../..')
    from lib import database_conf
    data_pd = query_postgre_table(config=database_conf, sql="SELECT * FROM jiulongpo_qiye")





