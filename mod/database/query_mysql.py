# -*- coding: utf-8 -*-
"""
Created on 2025/6/14 下午3:33

@Project -> File: srv-end-of-pipe-pollution-data-verification -> query.py

@Author: vincent-cj

@Describe:
"""
import pymysql
import pandas as pd


def query_mysql_table(config, sql):
    """
    查询postgre数据库中的表
    :param config:
    :param sql:
    :return:
    """
    # 创建连接
    connection = pymysql.connect(**config)
    try:
        # 创建游标对象
        with connection.cursor() as cursor:
            cursor.execute(sql)
            results = cursor.fetchall()  # 获取所有查询结果

            # 获取表头信息
            table_header = [desc[0] for desc in cursor.description]
            data_pd = pd.DataFrame(results, columns=table_header)
        return data_pd
    finally:
        connection.close()


if __name__ == '__main__':
    import sys
    sys.path.append('../..')
    from lib import database_conf
    sql_zx = """
        SELECT
            m.monitoring_time as "监测时间",m.flag_name as "工况状态",m.flue_gas_speed as "烟气流速(米/秒)",m.flue_gas_speed_flag as "烟气流速状态",m.exhaust_gas as "烟气流量(立方米/秒)",m.exhaust_gas_flag as "烟气流量状态",m.flue_gas_temperature as "烟气温度(摄氏度)",m.flue_gas_temperature_flag as "烟气温度状态",m.oxygen_content as "含氧量(%)",m.oxygen_content_flag as "含氧量状态",m.flue_gas_humidity as "烟气湿度(%)",m.flue_gas_humidity_flag as "烟气湿度状态",m.flue_gas_pressure as "烟气压力(千帕)",m.flue_gas_pressure_flag as "烟气压力状态",m.smoke as "烟尘实测值(毫克/立方米)",m.smoke_flag as "烟尘状态",m.so2 as "二氧化硫(毫克/立方米)",m.so2flag as "二氧化硫状态",m.nitrogen_oxide as "氮氧化物(毫克/立方米)",m.nitrogen_oxide_flag as "氮氧化物状态",m.mn,
            h.pollution_source_name as "排污企业名"
        FROM data_emission_air_monitor_min m
        INNER JOIN (
            SELECT DISTINCT mn, pollution_source_name
            FROM data_emission_air_monitor_hour_summary
        ) h
        ON m.mn = h.mn limit 10;
        """
    data_pd = query_mysql_table(config=database_conf, sql= sql_zx)
    print(data_pd)
