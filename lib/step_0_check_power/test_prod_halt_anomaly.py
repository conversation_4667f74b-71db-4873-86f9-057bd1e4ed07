# -*- coding: utf-8 -*-
"""
停产标记异常检测功能单元测试

@Author: vincent-cj
@Date: 2025-07-29
"""
import sys
import pandas as pd
import numpy as np
sys.path.append('../..')

from lib.step_0_check_power.step_5_validate_prod_halt_flag_anomely_YCBJ1 import (
    filter_facility_skipped_data,
    detect_production_halt_anomaly
)
from lib.step_0_check_power.check_utils import merge_time_intervals


def test_filter_facility_skipped_data():
    """测试停产数据筛选函数"""
    print("=== 测试停产数据筛选函数 ===")
    
    # 创建测试数据
    skipped_data = pd.DataFrame({
        'id': [1, 2, 3, 4],
        'enterprise_name': ['企业A', '企业B', '企业A', '企业A'],
        'unmonitoring_info': ['企业', '监测点', '污染源类别', '监测点'],
        'unmonitoring_site': ['', '排口1', '', '排口1'],
        'unmonitoring_indicator': ['', '', '废气', ''],
        'start_time': ['2025-01-01 10:00:00', '2025-01-01 11:00:00', 
                      '2025-01-01 12:00:00', '2025-01-01 13:00:00'],
        'end_time': ['2025-01-01 12:00:00', '2025-01-01 13:00:00',
                    '2025-01-01 14:00:00', '2025-01-01 15:00:00']
    })
    
    # 测试参数
    ent_names_list = ['企业A']
    port_names_list = ['排口1']
    monitoring_type = '废气'
    
    # 执行筛选
    result = filter_facility_skipped_data(
        skipped_data, ent_names_list, port_names_list, monitoring_type
    )
    
    print(f"输入数据行数: {len(skipped_data)}")
    print(f"筛选结果行数: {len(result)}")
    print(f"筛选结果ID: {result['id'].tolist() if not result.empty else []}")
    
    # 预期结果：应该包含ID为1, 3, 4的记录
    expected_ids = [1, 3, 4]
    actual_ids = result['id'].tolist()
    
    print(f"预期ID: {expected_ids}")
    print(f"实际ID: {actual_ids}")
    print(f"测试通过: {set(actual_ids) == set(expected_ids)}")
    
    return set(actual_ids) == set(expected_ids)


def test_merge_time_intervals_comprehensive():
    """全面测试时间聚合函数"""
    print("\n=== 测试时间聚合函数 ===")
    
    # 测试用例1：完全重叠
    test1 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 11:00:00'), ['id1']),
        (pd.Timestamp('2025-01-01 10:30:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id2'])
    ]
    result1 = merge_time_intervals(test1)
    print(f"测试1 - 完全重叠: {len(result1) == 1}")
    
    # 测试用例2：部分重叠
    test2 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:30:00'), ['id1']),
        (pd.Timestamp('2025-01-01 10:25:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id2'])
    ]
    result2 = merge_time_intervals(test2)
    print(f"测试2 - 部分重叠: {len(result2) == 1}")
    
    # 测试用例3：相邻（5分钟内）
    test3 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:30:00'), ['id1']),
        (pd.Timestamp('2025-01-01 10:33:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id2'])
    ]
    result3 = merge_time_intervals(test3)
    print(f"测试3 - 相邻时间段: {len(result3) == 1}")
    
    # 测试用例4：分离
    test4 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:30:00'), ['id1']),
        (pd.Timestamp('2025-01-01 11:00:00'), pd.Timestamp('2025-01-01 11:30:00'), ['id2'])
    ]
    result4 = merge_time_intervals(test4)
    print(f"测试4 - 分离时间段: {len(result4) == 2}")
    
    return all([len(result1) == 1, len(result2) == 1, len(result3) == 1, len(result4) == 2])


def create_test_data():
    """创建测试数据"""
    # 创建df_scheme测试数据
    df_scheme = pd.DataFrame({
        'enterprise_id': ['ent1'],
        'ue_enterprise_id': ['ue_ent1'],
        'ue_enterprise_name': ['测试企业'],
        'monitoring_site_data': ['排口1'],
        'monitoring_site_id': ['site1'],
        'monitoring_type': ['废气'],
        'enterprise_name_list': [['测试企业']],
        'monitoring_site_list': [['排口1']],
        'line_id': ['line1'],
        'line_name': ['生产线1']
    })
    
    # 创建skipped_data测试数据
    skipped_data = pd.DataFrame({
        'id': [1, 2],
        'enterprise_name': ['测试企业', '测试企业'],
        'unmonitoring_info': ['企业', '监测点'],
        'unmonitoring_site': ['', '排口1'],
        'unmonitoring_indicator': ['', ''],
        'start_time': ['2025-01-01 10:00:00', '2025-01-01 14:00:00'],
        'end_time': ['2025-01-01 12:00:00', '2025-01-01 16:00:00']
    })
    
    # 创建df_ue_info测试数据
    df_ue_info = pd.DataFrame({
        'ue_enterprise_id': ['ue_ent1'],
        'line_id': ['line1'],
        'category_code': ['PRODUCTION_LINK'],
        'facility_id': ['fac1'],
        'facility_name': ['生产设施1'],
        'reference_power': [100.0],
        'use_rated_power': [True]
    })
    
    # 创建df_ue_data测试数据（模拟异常情况：在停产时间段内功率较高）
    time_range = pd.date_range('2025-01-01 09:00:00', '2025-01-01 17:00:00', freq='5Min')
    df_ue_data = pd.DataFrame({
        'facility_id': ['fac1'] * len(time_range),
        'monitor_time_rounded': time_range,
        'active_power': [
            # 9:00-10:00: 正常低功率
            *[20.0] * 12,
            # 10:00-12:00: 停产时间段内高功率（异常）
            *[80.0] * 24,
            # 12:00-14:00: 正常低功率
            *[25.0] * 24,
            # 14:00-16:00: 停产时间段内高功率（异常）
            *[90.0] * 24,
            # 16:00-17:00: 正常低功率
            *[15.0] * 13
        ],
        'is_long_missing': [False] * len(time_range)
    })
    
    return df_scheme, skipped_data, df_ue_info, df_ue_data


def test_detect_production_halt_anomaly():
    """测试异常检测核心逻辑"""
    print("\n=== 测试异常检测核心逻辑 ===")
    
    # 创建测试数据
    df_scheme, skipped_data, df_ue_info, df_ue_data = create_test_data()
    
    # 执行异常检测
    start_dt = pd.Timestamp('2025-01-01 09:00:00')
    end_dt = pd.Timestamp('2025-01-01 17:00:00')
    
    anomaly_clues = detect_production_halt_anomaly(
        df_scheme=df_scheme,
        skipped_data=skipped_data,
        df_ue_info=df_ue_info,
        df_ue_data=df_ue_data,
        start_dt=start_dt,
        end_dt=end_dt,
        min_anomaly_minutes=30,
        prod_rated_ratio=0.5,
        prod_stat_ratio=0.8
    )
    
    print(f"检测到异常线索数量: {len(anomaly_clues)}")
    
    if anomaly_clues:
        for i, clue in enumerate(anomaly_clues, 1):
            print(f"异常线索{i}:")
            print(f"  设施: {clue['facility_name']}")
            print(f"  时间: {clue['alert_start_time']} - {clue['alert_end_time']}")
            print(f"  阈值: {clue['threshold']} kW")
            print(f"  平均功率: {clue['avg_power']} kW")
    
    # 预期应该检测到2个异常线索（两个停产时间段内都有高功率）
    expected_count = 2
    print(f"预期异常数量: {expected_count}")
    print(f"测试通过: {len(anomaly_clues) == expected_count}")
    
    return len(anomaly_clues) == expected_count


def run_all_tests():
    """运行所有测试"""
    print("开始运行停产标记异常检测功能测试...")
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_filter_facility_skipped_data())
    test_results.append(test_merge_time_intervals_comprehensive())
    test_results.append(test_detect_production_halt_anomaly())
    
    # 汇总结果
    passed_count = sum(test_results)
    total_count = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"总测试数: {total_count}")
    print(f"通过数: {passed_count}")
    print(f"失败数: {total_count - passed_count}")
    print(f"通过率: {passed_count/total_count*100:.1f}%")
    
    if passed_count == total_count:
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败，请检查代码逻辑")
    
    return passed_count == total_count


if __name__ == '__main__':
    run_all_tests()
