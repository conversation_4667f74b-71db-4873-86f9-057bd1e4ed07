# 停产标记异常检测功能需求文档

## 核心目标
检测在未监测说明(skipped_data)中标记为停产的时间段内，生产设施实际仍在运行的异常情况。

**异常判定条件**：
- 在未监测说明标记的停产时间内
- 生产设施的实际有功功率 >= 停产阈值（参考功率 × 比例系数）
- 异常状态持续时间 >= 30分钟

**输出要求**：
输出异常情况下所有生产设施的基础信息、异常时间段、停产标记数据的相关信息，并生成监测图表。
## 数据结构说明

### 主要数据变量定义

**1. df_map - 数据源映射关系**
- 用能数据源(UE)与自行监测数据源(Monitor)的映射关系
- UE字段：`ue_enterprise_id`, `ue_enterprise_name`, `line_id`, `line_name`
- Monitor字段：`enterprise_id`, `monitoring_site_data`, `monitoring_site_id`

**2. df_scheme - 自行监测基础信息**
- 聚合了df_map信息的自行监测数据源基础信息
- `enterprise_name_list`：企业在自行检测数据源的所有别名列表
- `monitoring_site_id`：排口在自行监测数据源的所有别名列表

**3. skipped_data - 未监测说明数据**
- 每行代表一个停产时间段
- 关键字段：
  - `id`：数据行标识（可重命名为skip_data_id）
  - `enterprise_name`：企业名称
  - `monitoring_type`：排污类别（废气、废水、噪声等）
  - `unmonitoring_site`：排口名
  - `unmonitoring_indicator`：排污因子名
  - `start_time`, `end_time`：停产时间段起止时间
  - `unmonitoring_info`：停产层级（企业级、排污类型级、排口级、排污因子级）

**4. df_ue_info - 用能数据源基础信息**
- `facility_id`, `facility_name`：设施ID和名称
- `category_code`：设施类型（PRODUCTION_LINK=生产设施，GOVERNANCE_LINK=治理设施）
- `reference_power`：参考功率，用于判断设施运行状态
- `use_rated_power`：参考功率类型标识（True=额定功率，False=统计功率）

**5. df_ue_data - 用能监测数据**
- `monitor_time_rounded`：圆整到5分钟的时间戳
- `voltage_a/b/c`：三相电压，`max_voltage`：最大电压
- `active_power`：有功功率（核心判断字段）
- `is_long_missing`：长时间缺失标记（True时不参与异常校验）

### 停产阈值计算规则
- 额定功率参考：`threshold = reference_power × 0.5`
- 统计功率参考：`threshold = reference_power × 0.8`

## 核心处理流程

### 主要处理步骤

**步骤1：按排口循环处理**
- 以企业的每个排口为单位进行循环处理

**步骤2：获取停产数据并聚合**
- 从df_scheme获取企业别名列表、排口别名列表及排污列表
- 调用filter_facility_skipped_data函数获取与该排口相关的所有停产数据
- 对停产时间段进行聚合（如：0~5, 4~8, 10~15 合并为 0~8 与 10~15）
- 返回聚合后的时间段与对应的数据ID

**步骤3：获取生产设施信息**
- 从df_scheme获取该排口的所有生产线
- 从df_ue_info中筛选所有生产线对应的生产设施（category_code='PRODUCTION_LINK'）

**步骤4：设施异常检测**
对每个生产设施执行以下检测：
- 根据聚合后的停产时间区间，从df_ue_data筛选对应时间段的监测数据
- 判断设施运行状态：`active_power >= threshold`
  - 额定功率：`threshold = reference_power × 0.5`
  - 统计功率：`threshold = reference_power × 0.8`
- 过滤掉is_long_missing=True的数据（不参与校验）
- 检测连续开机时间：持续开机≥30分钟则判定为停产标记异常

**步骤5：异常线索生成**
对存在异常的生产设施，生成包含以下信息的异常线索：
- 基础信息：ue_enterprise_id, ue_enterprise_name, enterprise_id, line_id, line_name
- 排口信息：monitoring_site_data, monitoring_site_id
- 设施信息：facility_id, facility_name
- 异常信息：skip_data_id, alert_start_time, alert_end_time
- 功率信息：停产限值, 该时段平均功率

**步骤6：绘制监测图表**
- 为每条异常线索绘制有功功率监测图
- 图表标题：停产标记异常
- 参考线：reference_power
- 文件名包含：企业名、产线名、设施名、异常时间段等标识

## 输出格式和技术要求

### 异常线索输出格式
将所有异常线索组成字典格式：
```python
{
    "items": [异常线索列表],
    "num": 异常线索数量
}
```

### 时间处理要求
- **任务时间扩展**：为避免漏掉跨任务的异常线索，将起始时间前移：
  ```python
  start_dt = pd.to_datetime(start_time) - pd.DateOffset(minutes=min_anomaly_minutes - time_granularity)
  ```
- **绘图时间扩展**：绘图时在异常时间段基础上前后各扩展30分钟

### 绘图功能优化要求

**1. draw_and_post_img函数优化**
新增参数：
- `alert_start_time`：异常开始时间，在图中显示为垂直虚线
- `alert_end_time`：异常结束时间，在图中显示为垂直虚线
- `reference_desc`：参考线描述，显示在图例中

**2. 垂直虚线样式要求**
- 颜色与横向虚线一致
- 透明度与横向虚线保持一致
- 与实线曲线颜色区分

**3. 调用参数设置**
- step_1中：`reference_desc = "治理设施参考功率"`
- step_5中：`reference_desc = "生产设施参考功率"`

## 实现约束条件

### 代码修改范围
- **data_utils.py**：尽量不要改动
- **step_1文件**：除draw_and_post_img调用外，其他不要改动
- **check_utils.py**：可以添加新的工具函数
- **step_5文件**：实现主要业务逻辑

### 参考代码模块
- step_1_validate_gov_abnormal_shutdown_LJYC1.py：异常校验逻辑参考
- data_utils.py：数据请求与处理参考
- check_utils.py：通用校验工具函数参考