核心目标：未监测说明(skipped_data)中标记的是停产的时间段，在未监测说明标记的停产时间内，如果对应的生成设施的实际有功功率大于等于停产阈值，则判定为停产标记异常（标记停产，但实际在生产），输出异常情况下所有生成设施的基础信息、对应的异常时间段、停产标记数据的相关信息。think harder. think ultra。先制定详细的规则和列举实现方案与逻辑，待用户确认后再编写代码。
具体的细则和边界条件处理逻辑如下：

- 脚本step_5_validate_prod_halt_flag_anomely_YCBJ1.py中已经导入了定义好的数据获取的函数，并以注释的方式列出了各个数据变量（pd.DataFrame）的字段；以及已经定义了筛选特定排口未监测说明数据的函数

- 主要df格式数据变量的含义如下：

  - df_map，用能数据源（use energe, ue）与自行监测数据源（self monitor）基础信息的映射关系，'ue_enterprise_id', 'ue_enterprise_name','line_id', 'line_name'为ue数据源的，'enterprise_id', 'monitoring_site_data', 'monitoring_site_id'为自行监测monitor数据源的。line_id与line_name为企业产线的id与名称，monitoring_site_data与monitoring_site_id为污染治理后残留污染物排放口的名称与id。
  - df_scheme为自行监测数据源的基础信息，并将df_map信息聚合了进去，enterprise_name_list代表该企业在自行检测数据源的所有别名列表，monitoring_site_id代表在自行监测数据源里排口的所有别名形成的列表
  - skipped_data为自行检测数据源中未监测说明，里面一行数据代表一个停产时间段。id代表该数据行的标识，每一行数据的id都不同，你可以重命名为skip_data_id等名字来专门区分。enterprise_name，monitoring_type，unmonitoring_site，unmonitoring_indicator分别代表自行监测中企业名、排污类别（如废气、废水、噪声等）、排口名、排污因子名（如PH值、SO2等）。'start_time', 'end_time'代表停产时间段的起止时间。unmonitoring_info标记停产的层级，未监测说明将停产的层级分为四级：企业级、排污类型级、排口级、排污因子级别，停产范围依次由大到小。unmonitoring_info代表停产的排口，unmonitoring_indicator代表停产的排污因子或排污类别。后续使用该数据的时候，以排口为单位进行遍历，需要找到当前企业、停产范围包含当前排口的所有停产记录。如filter_facility_skipped_data函数所示。
  - df_ue_info为用能数据源的基础信息，facility_id代表设施的id，facility_name代表设施的名称。设施包括生产与治理设施，由category_code进行区分标记，PRODUCTION_LINK代表生产设施，GOVERNANCE_LINK代表治理设施，当前需求只关注生成设施。reference_power代表参考功率，用于判断设施实际生成的状态，当实际功率低于参考功率*某个比例时，判断为关机，反之判断为开机。use_rated_power用于判定参考功率是否为额定功率，如果为True则判断为额定功率，False为统计功率，后续在判断生产设施是否正常工作时，如果是额定功率作为参考功率，则比例系数prod_rated_ratio为0.5， 如果是统计功率，则比例系数prod_stat_ratio为0.8
  - df_ue_data为为用能数据源的监测数据，monitor_time_rounded为已经圆整到5分钟的时刻，格式pd.TimeStamp，原始圆整的规则为：0~4分圆整到0分，0=5~9分圆整到5分，以此类推。'voltage_a',  'voltage_b', 'voltage_c'为三相电的电压，max_voltage为三者的最大值。active_power为有功功率，是当前需求中最关键的字段，用于判断生成设施是否正常工作的主要依据。is_long_missing为之前数据处理添加的标记，当数据连续缺失达到30分钟后，就会将is_long_missing标记为True，反之为False。标记为True的，后续的异常校验不用考虑（当作无异常处理）。

- 以企业的每个排口为单位进行循环

  - 从df_scheme中获取该企业的别名列表、排口别名列表及排污列表，并以此调用filter_facility_skipped_data函数（或修改完善该函数），获取与该排相关的所有停产数据及数据id，将所有数据进行时间上的聚合（如 0~5， 4~8， 10~15合并为 0~8与10~15）,返回聚合后的时间与数据id
  - 从df_scheme获取该排口的所有生产线
  - 从df_ue_info中获取所有生产线对应的所有生产设施（以category_code进行筛选）
  - 对每个生产设施进行进一步循环
    - 根据聚合后的停产时间区间，从df_ue_data筛选该设施在对应时间段所有用能监测数据
    - 调用judge_facility_status函数或参考该函数重新定义函数，判断该生成设施是否正常运行（active_power >= reference_power * prod_rated_ratio if use_rated_power else reference_power * prod_stat_ratio，满足此条件的为正常生产）,注意过滤掉is_long_missing标记为True的数据（此数据不参与校验，即该数据不在判断是否存在停产标记异常的数据范围内）
    - 对于开机的设施，如果持续时间达到30分钟，则说明该设施存在停产标记异常（即满足未监测说明标记了停产的时间段内，生成设施处于开启状态）

  - 对每个生产设施，如果存在停产标记异常的时间段，则返回ue_enterprise_id, ue_enterprise_name，enterprise_id ，line_id，line_name ， monitoring_site_data，monitoring_site_id ， facility_id，facility_name， 企业未监测说明里的数据id（skip_data_id)  ，  报警时段开始与结束时间（停产标记异常区间的开始时间与截止时间, alert_start_time, alert_end_time）， 停产限值 （reference_power * prod_rated_ratio if use_rated_power else reference_power * prod_stat_ratio），    该时段生产设施平均功率  （报警时间段内active_power的均值）
  - 对于每条异常线索（某个生产设施的某个停产标记异常的时间段），调用draw_and_post_img绘制监测该生产设施在当前异常时间段内的有功功率值，此时是一个设施的一个异常时间段绘制一张图，因此field_labels只有当前设施的active_power，title为停产标记异常。reference_line为参考功率reference_power。文件名中的标识需要带上企业名、产线名（或id）、生产设施名、异常线索起止时间等标签。具体的调用可以参看step_1_validate_gov_abnormal_shutdown_LJYC1.py脚本中get_gov_facility_anomalies函数。

- 将所有企业所有排口所有生产设施的异常线索组成一个列表形式，并放在一个异常线索字典中，对应key为items，num为异常线索的数目。

- 为了避免漏掉跨前后两次任务的异常线索，将本次任务的起始时间往前扩展异常线索的最小持续时间，start_dt = pd.to_datetime(start_time) - pd.DateOffset(minutes = min_anomaly_minutes - time_granularity)

- 绘图的时候，参考get_gov_facility_anomalies，将时间在任务起止时间的基础上，各扩展30分钟。

- 优化绘图工具函数draw_and_post_img，新增alert_start_time与alert_start_time，由于绘图的时候在异常线索时间段的基础上前后各扩展了30分钟，因此在横轴上alert_start_time与alert_start_time所在的位置，加上两条垂直的透明度较高虚线，这里左右两条垂直的虚线颜色一致，且透明度与横向的虚线保持一致（记得图里的三种虚线的颜色与实线曲线的颜色予以区分），用于标记异常线索的开始与截止时间。新增reference_desc代表横向的参考线的描述，在图例中新增对于reference_desc的显示。所新增的三个参数都可以默认为None，如果为None，则在图中不予显示。

- 优化get_gov_facility_anomalies对于draw_and_post_img的调用，新增alert_start_time与alert_start_time及reference_desc的传参，其中reference_desc为：治理设施参考功率

- step_5_validate_prod_halt_flag_anomely_YCBJ1.py中对于draw_and_post_img的调用，参考get_gov_facility_anomalies的调用，将所有参数加上，其中reference_desc为：生产设施参考功率

- 编写异常校验代码，放置于step_5_validate_prod_halt_flag_anomely_YCBJ1.py脚本中，可以参考step_1_validate_gov_abnormal_shutdown_LJYC1.py（另一条校验需求）、data_utils.py（数据请求与处理）、check_utils.py（通用的校验工具函数）进行任务逻辑的编写。对于data_utils.py尽量不要改动，step_1_validate_gov_abnormal_shutdown_LJYC1.py除了对于draw_and_post_img的调用，其他尽量不要改动，check_utils.py可以添加你所新增的工具函数。