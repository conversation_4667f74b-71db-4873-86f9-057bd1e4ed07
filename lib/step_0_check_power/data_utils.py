# -*- coding: utf-8 -*-
"""
Created on 2025/7/22 下午3:24

@Project -> File: srv-enterprise-power-usage-verify -> data_utils.py

@Author: vincent-cj

@Describe:
"""
import sys
import numpy as np
import pandas as pd
import warnings
from collections.abc import Iterable

sys.path.append('../..')

from lib import proj_dir, database_conf, logger, logging_decorator, logging_decorator_arg
from mod.database.query_postgre_table import query_postgre_table
from lib.step_0_check_power.check_utils import replace_code_suffix

warnings.filterwarnings("ignore")


def interpolate_facility_data(
        facility_data: pd.DataFrame,
        five_days_before: pd.Timestamp,
        end_dt: pd.Timestamp,
        time_granularity: int,
        max_missing_minutes: int
):
    """
    6. 为每个设施生成完整时间序列并插值

    Args:
        facility_data (_type_): _description_
        five_days_before (_type_): _description_
        end_dt (_type_): _description_
        time_granularity (_type_): _description_
        max_missing_minutes (_type_): _description_

    Returns:
        _type_: _description_
    """

    # 生成完整时间序列
    time_range = pd.date_range(start = five_days_before, end = end_dt, freq = '5Min')
    full_time_df = pd.DataFrame({'monitor_time_rounded': time_range})

    # 合并数据
    merged = full_time_df.merge(facility_data, on = 'monitor_time_rounded', how = 'left')

    # 识别连续缺失达到30分钟的区间
    merged['is_missing'] = merged['active_power'].isna()
    merged['missing_group'] = (merged['is_missing'] != merged['is_missing'].shift()).cumsum()

    # 计算每个缺失组的持续时间
    missing_groups = merged[merged['is_missing']].groupby('missing_group').size() * time_granularity  # 5分钟粒度
    long_missing_groups = missing_groups[missing_groups >= max_missing_minutes].index

    # 标记长时间缺失的数据不进行插值
    long_missing_mask = merged['missing_group'].isin(long_missing_groups) & merged['is_missing']

    # 对非长时间缺失的数据进行插值
    for col in ['active_power', 'voltage_a', 'voltage_b', 'voltage_c']:
        merged.loc[~long_missing_mask, col] = merged.loc[~long_missing_mask, col].interpolate(method = 'linear')

    # 标记长时间缺失时段，用于后续在设施状态判断中将其置为False
    merged['is_long_missing'] = long_missing_mask

    return merged.drop(['is_missing', 'missing_group'], axis = 1)


@logging_decorator
def process_ue_data(
        df_ue_info: pd.DataFrame,
        df_ue_data: pd.DataFrame,
        start_time: str,
        end_time: str,
        days_offset: int = 5,
        max_missing_minutes: int = 30,
        time_granularity: int = 5,
        min_valid_voltage: int = 200,
        min_valid_power: float = 0.5,
        min_anomaly_minutes: int = 30,
        extension_minutes: int = 30
):
    """
    获取hist与pre的功率统计值用于参考功率至于df_ue_info
    对监测数据按设施进行插值处理
    Args:
        df_ue_info (pd.DataFrame): Enterprise facility information.
        df_ue_data (pd.DataFrame): Monitoring data.
        start_time (str): Task start time in 'YYYY-MM-DD HH:MM:SS' format.
        end_time (str): Task end time in 'YYYY-MM-DD HH:MM:SS' format.
        days_offset (int): Number of days to look back from the start time. Default is 5.
        max_missing_minutes (int): Maximum allowed consecutive missing minutes. Default is 30.
        time_granularity (int): Time granularity in minutes. Default is 5.
        min_valid_voltage (int): Minimum valid voltage threshold. Default is 200.
        min_valid_power (float): Minimum valid power threshold. Default is 0.5.
        min_anomaly_minutes (int): Minimum duration in minutes for an anomaly to be considered valid. Default is 30.
        extension_minutes (int): Minutes to extend the time range for data extraction. Default is 30.
    """
    # 为了避免漏掉跨前后两次任务的异常线索，将本次任务的起始时间往前扩展异常线索的最小持续时间
    start_dt = pd.to_datetime(start_time) - pd.DateOffset(minutes = min_anomaly_minutes - time_granularity)
    end_dt = pd.to_datetime(end_time)
    n_days_before = start_dt - pd.DateOffset(days = days_offset)

    # ============ 参考功率获取 ============
    # 1. 读取历史功率统计数据
    hist_desc = pd.read_csv(f'{proj_dir}/file/power_data_desc.csv',
                            usecols = ['ue_enterprise_id', 'facility_id', 'mean'],
                            dtype = {'ue_enterprise_id': str, 'facility_id': str, 'mean': float}
                            )
    # 将hist_desc中mean为np.nan的替换为0
    hist_desc['mean'] = hist_desc['mean'].fillna(0)
    hist_desc = hist_desc.rename(columns = {'mean': 'hist_mean_power'})
    hist_desc = hist_desc.drop_duplicates(subset = ['ue_enterprise_id', 'facility_id'])

    # 转换数值字段
    df_ue_info['rated_power'] = pd.to_numeric(df_ue_info['rated_power'], errors = 'coerce')
    df_ue_info['facility_sequence_number'] = pd.to_numeric(df_ue_info['facility_sequence_number'], errors = 'coerce')

    # 过滤监测数据到任务时间范围（包括前5天用于计算均值，以及后30分钟用于绘图）
    extended_end_time = end_dt + pd.DateOffset(minutes = extension_minutes)
    df_ue_data = df_ue_data[(df_ue_data['monitor_time'] >= n_days_before) &
                            (df_ue_data['monitor_time'] <= extended_end_time)]

    # 3. 时间圆整到5分钟粒度
    df_ue_data['monitor_time_rounded'] = df_ue_data['monitor_time'].dt.floor('5Min')

    # 4. 过滤无效数据：active_power < 0.5 且最大电压 < 200的数据
    voltage_cols = ['voltage_a', 'voltage_b', 'voltage_c']
    df_ue_data['max_voltage'] = df_ue_data[voltage_cols].apply(lambda x: np.nanmax(x)
    if x.notna().any() else 0, axis = 1)
    invalid_mask = ((df_ue_data['active_power'] < min_valid_power) &
                    (df_ue_data['max_voltage'] < min_valid_voltage))
    df_ue_data = df_ue_data[~invalid_mask]  # 不合理的数据以缺失处理

    # 5. 按企业、设施、时间分组聚合
    df_ue_data = df_ue_data.groupby(['ue_enterprise_id', 'facility_id', 'monitor_time_rounded']).agg({
        col: np.nanmean for col in voltage_cols + ['active_power', 'max_voltage']
    }).reset_index()

    # 7. 计算每个设施的前5日统计均值，截止时间以任务的截止时间为准
    pre_5d_means = calculate_pre_days_mean_power(df_ue_data, n_days_before,
                                                 end_dt, min_valid_power,
                                                 days_offset)

    # 8. 合并历史均值和前5日均值，取最大值作为统计均值
    df_ue_info = df_ue_info.merge(hist_desc, on = ['ue_enterprise_id', 'facility_id'], how = 'left')
    df_ue_info = df_ue_info.merge(pre_5d_means, on = ['ue_enterprise_id', 'facility_id'], how = 'left')

    # 统计均值 = max(历史均值, 前5日均值)
    df_ue_info['stat_mean_power'] = df_ue_info[['hist_mean_power', 'pre_nd_mean_power']].apply(
        lambda x: np.nan if x.isna().all() else np.nanmax(x), axis = 1
    )

    # 9. 确定每个设施的判断阈值（优先使用额定功率，否则使用统计均值）
    df_ue_info['use_rated_power'] = df_ue_info['rated_power'] > 0
    df_ue_info['reference_power'] = df_ue_info['rated_power'].fillna(df_ue_info['stat_mean_power'])
    # 将reference_power小于等于0的置为np.nan
    df_ue_info['reference_power'] = df_ue_info['reference_power'].where(
        df_ue_info['reference_power'] > 0, np.nan
    )

    # ============ 对每个设施应用插值处理 ============
    facility_groups = df_ue_data.groupby(['ue_enterprise_id', 'facility_id'])
    interpolated_data = []
    for (enterprise_id, facility_id), group in facility_groups:
        interpolated = interpolate_facility_data(group, n_days_before, extended_end_time,
                                                 time_granularity, max_missing_minutes)
        interpolated['ue_enterprise_id'] = enterprise_id  # 填充空白行的企业与设施id
        interpolated['facility_id'] = facility_id
        interpolated_data.append(interpolated)

    df_ue_data = pd.concat(interpolated_data, ignore_index = True)
    return df_ue_info, df_ue_data


def calculate_pre_days_mean_power(
        df_data: pd.DataFrame,
        five_days_before: pd.Timestamp,
        end_dt: pd.Timestamp,
        min_valid_power: float,
        days_offset: int
):
    """Calculate the 5-day mean power for each facility."""
    pre_task_data = df_data[(df_data['monitor_time_rounded'] >= five_days_before) &
                            (df_data['monitor_time_rounded'] <= end_dt)]

    def compute_mean(group):
        valid_rows = group[group['active_power'] >= min_valid_power]
        if valid_rows.empty:
            return np.nan
        start_time = valid_rows.iloc[0]['monitor_time_rounded']
        end_time = start_time + pd.DateOffset(days = days_offset)
        subset = group[(group['monitor_time_rounded'] >= start_time) &
                       (group['monitor_time_rounded'] <= end_time)]
        if subset.empty:
            return np.nan
        return subset['active_power'].mean()

    pre_5d_means = pre_task_data.groupby(['ue_enterprise_id', 'facility_id']).apply(compute_mean).reset_index()
    pre_5d_means.columns = ['ue_enterprise_id', 'facility_id', 'pre_nd_mean_power']
    return pre_5d_means


@logging_decorator_arg
def request_ue_data_from_bd(
        province_pinyin: str,
        area_code: str,
        start_time: str,
        end_time: str,
        days_offset: int = 5,
        extension_minutes: int = 30,
        all_ent_names_list: list = None
) -> pd.DataFrame:
    """获取指定省份和区域代码的企业用电数据

    Args:
        province_pinyin (str): 省份拼音
        area_code (str): 区域代码
        start_time (str): 任务开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
        end_time (str): 任务结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
        days_offset (int, optional): 在任务开始时间基础上往前回溯的天数. Defaults to 5.
        extension_minutes (int, optional): 扩展的分钟数，用于绘图时前后各扩展. Defaults to 30.
        all_ent_names_list (list, optional): 企业名称列表. Defaults to None.

    Returns:
        pd.DataFrame: _description_
    """
    # 将start_time往前推5天，用于计算前五天的统计均值，并重新转为字符串格式
    start_time = pd.to_datetime(start_time) - pd.DateOffset(days = days_offset)
    start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
    # 将end_time往后推30分钟，用于绘图，并重新转为字符串格式
    end_time = pd.to_datetime(end_time) + pd.DateOffset(minutes = extension_minutes)
    end_time = end_time.strftime('%Y-%m-%d %H:%M:%S')

    # ------------ Extracting enterprise and line info from PostgreSQL database ------------
    pre_code = replace_code_suffix(area_code)
    info_cols = ['ue_enterprise_id', 'facility_id', 'category_code', 'line_id',
                 'ue_enterprise_name', 'line_name', 'facility_name', 'source',
                 'facility_sequence_number', 'rated_power', 'is_gas_related',
                 'region_code', 'uscc'
                 ]

    sql_info = f"SELECT {','.join(info_cols)} FROM ue_basic_information_product_line_{province_pinyin} " \
               f"WHERE region_code LIKE '{pre_code}%'"
    if all_ent_names_list is not None:
        all_ent_str = ','.join(map(lambda x: f"'{str(x)}'", all_ent_names_list))
        sql_info += f" AND ue_enterprise_name IN ({all_ent_str})"
    else:
        all_ent_str = ''
    df_ue_info = query_postgre_table(database_conf, sql_info)

    # 将 None null nan - 等异常字符转为 np.nan
    df_ue_info = df_ue_info.map(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null']
    else str(x))

    # 以info_cols的前四列为条件，筛选出非空的行,并以这些列为条件进行去重
    df_ue_info = df_ue_info[df_ue_info[info_cols[:4]].notna().all(axis = 1)]
    df_ue_info = df_ue_info.drop_duplicates(subset = info_cols[:4])
    if df_ue_info.empty:
        raise ValueError(f'区域{area_code}获得的用能数据源中的企业基础信息为空')

    # 按照ue_enterprise_id和facility_id进行分组聚合，line_id字段生成一个列表，其他info_cols中的字段取第一个不为空的值
    agg_dict = {col: (lambda x: x.dropna().iloc[0] if x.dropna().size > 0 else '') for col in info_cols}
    agg_dict.update({col: lambda x: '&|&'.join(x.dropna().unique()) for col in ['line_id', 'line_name']})
    # agg_dict.update({col: lambda x: list(x.dropna().unique()) for col in ['line_id', 'line_name']})
    df_ue_info = df_ue_info.groupby(info_cols[:3], as_index = False).agg(agg_dict)

    # -------------- Extracting monitor data from PostgreSQL database --------------
    data_cols = ['ue_enterprise_id', 'facility_id', 'monitor_time', 'active_power',
                 'voltage_a', 'voltage_b', 'voltage_c'
                 ]
    sql_data = f"SELECT {','.join(data_cols)} FROM ue_basic_data_monitoring_data_{province_pinyin} " \
               f"WHERE region_code LIKE '{pre_code}%' AND time_type = '100' " \
               f"AND monitor_time >= '{start_time}' " \
               f"AND monitor_time <= '{end_time}'"
    if all_ent_names_list is not None:
        sql_data += f" AND ue_enterprise_name IN ({all_ent_str})"
    df_ue_data = query_postgre_table(database_conf, sql_data)

    # 将 None null nan - 等异常字符转为 np.nan
    for col in data_cols[:2]:
        df_ue_data[col] = df_ue_data[col].map(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null']
        else str(x))

    # 将'active_power', 'voltage_a', 'voltage_b', 'voltage_c'转为浮点型，错误的转为nan
    for col in ['active_power', 'voltage_a', 'voltage_b', 'voltage_c']:
        df_ue_data[col] = pd.to_numeric(df_ue_data[col], errors = 'coerce')

    # 将monitor_time转为datetime格式
    df_ue_data['monitor_time'] = pd.to_datetime(df_ue_data['monitor_time'], errors = 'coerce')

    # # 以data_cols的前三列为条件，筛选出非空的行,并以这些列进行分组，对其余字段取np.nanmean
    df_ue_data = df_ue_data[df_ue_data[data_cols[:3]].notna().all(axis = 1)]
    agg_dict = {col: (np.nanmean if col != 'source' else 'first') for col in data_cols[3:]}
    df_ue_data = df_ue_data.groupby(data_cols[:3], as_index = False).agg(agg_dict)
    if df_ue_data.empty:
        raise ValueError(f'区域{area_code}获得的用能数据源为空，时间段为`{start_time}` -> `{end_time}`')

    return df_ue_info, df_ue_data


def get_ue_info_and_data(
        province_pinyin: str,
        area_code: str,
        start_time: str,
        end_time: str,
        days_offset: int = 5,
        extension_minutes: int = 30,
        all_ent_names_list: list = None
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """获取指定省份和区域代码的企业用电数据和企业基础信息

    Args:
        province_pinyin (str): 省份拼音
        area_code (str): 区域代码
        start_time (str): 任务开始时间，格式为 'YYYY-MM-DD HH:MM:SS'
        end_time (str): 任务结束时间，格式为 'YYYY-MM-DD HH:MM:SS'
        days_offset (int, optional): 在任务开始时间基础上往前回溯的天数. Defaults
        extension_minutes (int, optional): 扩展的分钟数，用于绘图时前后各扩展. Defaults to 30.
        all_ent_names_list (list, optional): 企业名称列表. Defaults to None.

    Returns:
        pd.DataFrame: _description_
    """
    df_ue_info, df_ue_data = request_ue_data_from_bd(
        province_pinyin = province_pinyin,
        area_code = area_code,
        start_time = start_time,
        end_time = end_time,
        days_offset = days_offset,
        extension_minutes = extension_minutes,
        all_ent_names_list = all_ent_names_list
    )

    df_ue_info, df_ue_data = process_ue_data(
        df_ue_info = df_ue_info,
        df_ue_data = df_ue_data,
        start_time = start_time,
        end_time = end_time,
        days_offset = days_offset,
        max_missing_minutes = extension_minutes,
    )
    return df_ue_info, df_ue_data


def request_self_monitor_scheme_from_db(area_pinyin, area_code, df_map: pd.DataFrame):
    """从数据库获取结构化方案的相关信息，并进行清洗与聚合"""
    pre_code = replace_code_suffix(area_code)

    def _drop_df_duplicated(df: pd.DataFrame, unique_fields = None):
        for field in ['id', 'recvtime']:
            if field in df.columns:
                df.drop(columns = field, inplace = True)
        df.drop_duplicates(inplace = True, subset = unique_fields)
        # df.dropna(subset = unique_fields, how = 'any', axis = 0, inplace = True)

    def first_non_nan(series):
        # 返回第一个非NaN的值
        return series.dropna().iloc[0] if not series.dropna().empty else np.nan

    # -------- 获取企业基础信息，并合并企业别名 -------
    all_ent_ids = df_map['enterprise_id'].unique().tolist()
    if not all_ent_ids:
        raise ValueError(f'区域{area_code}获得的用能数据源中的企业基础信息为空')
    all_ent_str = ','.join(map(lambda x: f"'{str(x)}'", all_ent_ids))
    ent_use_fields = ['enterprise_id', 'enterprise_name']
    ent_fields_str = ','.join(ent_use_fields)

    sql_ents_info = f"SELECT {ent_fields_str} FROM basic_information_enterprise_{area_pinyin} WHERE " \
                    f"region_code LIKE '{pre_code}%' AND enterprise_id IN ({all_ent_str})"


    df_ents_info = query_postgre_table(config = database_conf, sql = sql_ents_info)
    df_ents_info = df_ents_info.applymap(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null'] else x). \
        dropna(axis = 0, how = 'all')
    # 避免同一区域下、同一企业名存在不同的映射数据
    _drop_df_duplicated(df_ents_info, unique_fields = ['enterprise_name'])
    df_ents_info = df_ents_info.dropna(how = 'any', subset = ent_use_fields, axis = 0)

    # 别名聚合,并将新聚合的企业名称字段名重命名为enterprise_name_list
    groupby_fields = ['enterprise_id']
    merge_field = 'enterprise_name'
    df_ents_new = df_ents_info.groupby(groupby_fields).agg({
        merge_field: lambda x: x.unique().tolist(),  # merge_field字段取unique后转为列表
        **{col: first_non_nan for col in df_ents_info.columns
           if col not in groupby_fields + [merge_field]}  # 其他字段取第一个非NaN值
    }).reset_index().rename(columns={'enterprise_name': 'enterprise_name_list'})

    # -------- 获取企业id、排口id与因子id的映射关系 --------
    all_site_ids = df_map['monitoring_site_id'].unique().tolist()
    if not all_site_ids:
        raise ValueError(f'区域{area_code}获得的用能数据源中的监测站点基础信息为空')
    all_id_str = ','.join(map(lambda x: f"'{str(x)}'", all_site_ids))
    scheme_use_fields = ['enterprise_id', 'monitoring_site_id', 'std_code']
    scheme_fields_str = ','.join(scheme_use_fields)

    sql_schemes_info = f"SELECT {scheme_fields_str} FROM basic_information_scheme_emission_info_{area_pinyin} WHERE " \
                       f"region_code LIKE '{pre_code}%' AND enterprise_id IN ({all_ent_str}) " \
                       f"AND monitoring_site_id IN ({all_id_str})"
    df_schemes_info = query_postgre_table(config = database_conf, sql = sql_schemes_info)
    df_schemes_info = df_schemes_info.applymap(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null'] else x). \
        dropna(axis = 0, how = 'all')
    
    # 此处不用关注方案的版本，只需要获取企业、排口与因子的映射关系即可
    _drop_df_duplicated(df_schemes_info, unique_fields = scheme_use_fields)
    df_schemes_new = df_schemes_info

    # -------- 获取排口基础信息，并合并排口别名 -------
    port_use_fields = ['enterprise_id', 'monitoring_type', 'monitoring_site_id', 'monitoring_site']
    port_fields_str = ','.join(port_use_fields)
    sql_ports_info = f"SELECT {port_fields_str} FROM basic_information_outlet_{area_pinyin} WHERE " \
                     f"region_code LIKE '{pre_code}%' AND monitoring_site_id IN ({all_id_str})"
    df_ports_info = query_postgre_table(config = database_conf, sql = sql_ports_info)
    df_ports_info = df_ports_info.applymap(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null'] else x)

    port_use_fields = ['enterprise_id', 'monitoring_type', 'monitoring_site_id', 'monitoring_site']
    df_ports_info = df_ports_info[port_use_fields].dropna(how = 'any', axis = 0)
    # 避免同一企业、同一种污染类别下、同一排口存在重复的映射数据
    _drop_df_duplicated(df_ports_info, unique_fields = ['enterprise_id', 'monitoring_type', 'monitoring_site'])

    # 别名聚合
    df_ports_new = df_ports_info.groupby(by = port_use_fields[:-1])[port_use_fields[-1]].apply(
        lambda x: list(set(x.dropna()))
    ).reset_index().rename(columns = {'monitoring_site': 'monitoring_site_list'})

    # -------- 获取因子基础信息，并合并因子别名 -------
    all_species_ids = df_schemes_info['std_code'].unique().tolist()
    all_species_str = ','.join(map(lambda x: f"'{str(x)}'", all_species_ids))   
    sql_species_info = f"SELECT * FROM basic_information_std_factor_map where std_code in ({all_species_str})"
    df_species_info = query_postgre_table(config = database_conf, sql = sql_species_info)
    df_species_info = df_species_info.applymap(lambda x: np.nan if x in [None, '', 'nan', '-', 'None', 'null'] else x)

    # 名称与标准名为空的，互相替换
    mask1 = df_species_info['std_code'].isna()
    df_species_info.loc[mask1, 'std_code'] = df_species_info.loc[mask1, 'monitoring_indicator']
    mask1 = df_species_info['monitoring_indicator'].isna()
    df_species_info.loc[mask1, 'monitoring_indicator'] = df_species_info.loc[mask1, 'std_code']
    # 避免同一种污染类别下、同一污染因子存在重复的映射数据
    _drop_df_duplicated(df_species_info, unique_fields = ['monitoring_indicator', 'std_type'])

    specie_use_fields = ['std_code', 'std_name', 'std_type', 'monitoring_indicator']
    df_species_info = df_species_info[specie_use_fields].dropna(axis = 0, how = 'any')

    # 别名的聚合
    df_species_new = df_species_info.groupby(by = specie_use_fields[:-1])[specie_use_fields[-1]].apply(
        lambda x: list(set(x.dropna()))
    ).reset_index().rename(columns = {'monitoring_indicator': 'monitoring_indicator_list'})

    # -------- 合并企业与方案 -------
    def _merge_dfs(df_left: pd.DataFrame, df_right: pd.DataFrame, on_keys):
        """去除重叠的字段并按left方式进行合并"""
        drop_fields = set(df_left.columns).intersection(df_right.columns).difference(on_keys)
        df_right = df_right.drop(columns = drop_fields)
        df_scheme = pd.merge(df_left, df_right, how = 'inner', on = on_keys)
        return df_scheme

    scheme_on_key = ['enterprise_id']
    df_scheme = _merge_dfs(df_ents_new, df_schemes_new, scheme_on_key)

    port_on_key = ['enterprise_id', 'monitoring_site_id']
    df_scheme = _merge_dfs(df_scheme, df_ports_new, port_on_key)

    specie_on_key = ['std_code']
    df_scheme = _merge_dfs(df_scheme, df_species_new, specie_on_key).rename(columns = {'std_code': 'specie_id'})

    # 将用能数据源中的产线id与自行监测数据源的排口名列表进行匹配
    df_scheme = _merge_dfs(df_scheme, df_map, ['enterprise_id', 'monitoring_site_id'])
    # enterprise_name字段是一个列表，将df_scheme['enterprise_name']中所有企业名拼接成一个大的列表
    all_ent_names_list = get_all_ent_names_list(df_scheme)

    return df_scheme, all_ent_names_list


def get_all_ent_names_list(df_scheme: pd.DataFrame) -> list:
    """
    将df_scheme['enterprise_name']中所有企业名拼接成一个大的列表
    
    Args:
        df_scheme (pd.DataFrame): 包含企业信息的DataFrame
        
    Returns:
        list: 包含所有企业名的列表
    """
    if 'enterprise_name_list' not in df_scheme.columns:
        return []
    
    # 获取所有企业名称，处理可能为列表的情况
    all_names = set()
    for names in df_scheme['enterprise_name_list']:
        if isinstance(names, Iterable) and not isinstance(names, str):
            all_names.update(names)
        else:
            all_names.add(str(names))
    
    return list(all_names)


def get_ue_monitor_info_map(area_pinyin: str, area_code: str) -> pd.DataFrame:
    """获取用能数据源基础信息与自行监测基础信息的映射表
    Args:
        area_pinyin (str): 区域拼音
        area_code (str): 区域编码

    Returns:
        _type_: _description_
    """
    map_use_fields = ['ue_enterprise_id', 'ue_enterprise_name', 'enterprise_id', 
                      'monitoring_site_data', 'monitoring_site_id', 'line_id', 'line_name']
    map_fields_str = ','.join(map_use_fields)
    pre_code = replace_code_suffix(area_code)
    sql_map = f"SELECT {map_fields_str} FROM ue_basic_map_ue_map_jc_jx_monitor_data_{area_pinyin} WHERE " \
              f"region_code LIKE '{pre_code}%' AND monitoring_type = 'self_monitor'"
    df_map = query_postgre_table(database_conf, sql_map)

    cols = ['ue_enterprise_id', 'enterprise_id', 'monitoring_site_id', 'line_id']
    df_map = df_map.drop_duplicates(subset = cols).dropna(subset = cols, how = 'any')

    return df_map


def get_skipped_data(area_pinyin: str, area_code: str, start_time: str, end_time: str, all_ent_names_list: list):
    """获取指定区域和时间段内企业的停产/限产数据

    Args:
        area_pinyin (str): 区域拼音，用于构建数据库表名
        area_code (str): 区域编码，用于筛选指定区域的企业数据
        start_time (str): 查询开始时间，格式为'YYYY-MM-DD HH:MM:SS'
        end_time (str): 查询结束时间，格式为'YYYY-MM-DD HH:MM:SS'
        all_ent_names_list (list): 企业名称列表，用于筛选指定企业的停产/限产数据

    Returns:
        pandas.DataFrame: 包含企业停产/限产信息的数据框，包含以下列：
            - enterprise_name: 企业名称
            - start_time: 停产/限产开始时间
            - end_time: 停产/限产结束时间
            - monitoring_mode: 监测方式
            - monitoring_type: 监测类型
            - unmonitoring_site: 停产/限产点位
            - 其他相关字段
    """
    all_ent_str = ','.join(map(lambda x: f"'{str(x)}'", all_ent_names_list))
    pre_code = replace_code_suffix(area_code)

    sql_unmonitor = f"SELECT * FROM basic_data_unmonitor_explain_{area_pinyin} WHERE " \
        f"region_code like '{pre_code}%' AND " \
        f"end_time >= '{start_time}' AND start_time <= '{end_time}' AND " \
        f"enterprise_name IN ({all_ent_str});"
    skipped_data = query_postgre_table(config = database_conf, sql = sql_unmonitor)

    rename_dct2 = {
        # 'unmonitoring_site': 'monitoring_site_data',
        'enterprise_name': 'enterprise_name',
        'qishi_shijian': 'start_time',
        'jiezhi_shijian': 'end_time',
        'jiance_fangshi': 'monitoring_mode',
        'paikou_leixing': 'monitoring_type',
        'paikou': 'unmonitoring_site',
        'monitoring_method': 'monitoring_mode'

    }
    skipped_data.rename(columns = rename_dct2, inplace = True)

    # -------- 对监测方式和停产点位进行过滤 -------
    skipped_type_map = {
        'organized_gas': ['废气'],
        'unorganized_gas': ['无组织'],
        'water': ['废水'],
        'noise': ['噪声'],
        'surroundings': ['周边环境', '地下水', '土壤'],
        'vocs': ['voc']
    }
    if 'monitoring_mode' not in skipped_data.columns:
        skipped_data['monitoring_mode'] = '手工'
    skipped_data.loc[skipped_data['monitoring_mode'].isna(), :] = '手工'

    if 'monitoring_type' not in skipped_data.columns:
        skipped_data['monitoring_type'] = '所有'
    skipped_data.loc[skipped_data['unmonitoring_info'].str.contains('企业'), 'monitoring_type'] = '所有'
    mask = (skipped_data['monitoring_type'] == '所有') | \
           (skipped_data['monitoring_mode'] == '手工')
    skipped_data = skipped_data[mask]

    # 字段格式转换
    for col in skipped_data.columns:
        if 'time' in col:
            # 解决2099的问题
            skipped_data[col] = skipped_data[col].fillna('').astype(str).str.replace('2999', '2099')
            skipped_data[col] = pd.to_datetime(np.array(skipped_data[col], dtype='datetime64[ms]'))
        else:
            # 解决里面还有np.nan与None的问题
            skipped_data[col] = skipped_data[col].fillna('').astype(str)

    # 如果未监测说明的截止时间精确到天的，则默认延长至当天的最后1秒钟
    if all(pd.to_datetime(skipped_data['end_time']).dt.hour == 0):
        skipped_data.loc[:, 'end_time'] += pd.Timedelta(hours = 23, minutes = 59, seconds = 59)

    skipped_data.dropna(subset = ['enterprise_name', 'start_time', 'end_time'],
                        axis = 0, how = 'any', inplace = True)
    skipped_data = skipped_data.drop_duplicates(
        subset = ['enterprise_name', 'unmonitoring_info', 'unmonitoring_site',
                  'unmonitoring_indicator', 'start_time', 'end_time'],
        )
    return skipped_data


if __name__ == '__main__':
    from lib import city_code_pinyin_map

    # Example usage
    province_pinyin = 'chongqing'
    area_code = '500107000'
    city_name = city_code_pinyin_map[area_code]
    start_time = '2025-05-01 00:00:00'
    end_time = '2025-05-15 23:59:59'

    df_map = get_ue_monitor_info_map(province_pinyin, area_code)
    df_scheme, all_ent_names_list = request_self_monitor_scheme_from_db(province_pinyin, area_code, df_map)
    
    skipped_data = get_skipped_data(
        area_pinyin = province_pinyin,
        area_code = area_code,
        start_time = start_time,
        end_time = end_time,
        all_ent_names_list = all_ent_names_list
    )

    df_ue_info, df_ue_data = get_ue_info_and_data(
        province_pinyin = province_pinyin,
        area_code = area_code,
        start_time = start_time,
        end_time = end_time,
        all_ent_names_list = all_ent_names_list
    )

    print(df_ue_info.head())
    print(df_ue_data.head())
