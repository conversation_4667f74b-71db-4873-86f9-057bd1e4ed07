# -*- coding: utf-8 -*-
"""
Created on 2025/7/22 下午3:24

@Project -> File: srv-enterprise-power-usage-verify -> step_4_verify_prod_halt_flag_anomely.py

@Author: vincent-cj

@Describe:
"""
import sys
import numpy as np
import pandas as pd
sys.path.append('../..')
from lib.step_0_check_power.data_utils import (
    get_ue_monitor_info_map,
    request_self_monitor_scheme_from_db,
    get_skipped_data,
    get_ue_info_and_data
)


def filter_facility_skipped_data(
        skipped_data: pd.DataFrame, 
        ent_names_list, 
        port_names_list, 
        monitoring_type， 
        specie_names_list = None
):
    """
    过滤符合当前企业、排污类别、排口的未生产记录
    :param skipped_data: 总停产记录
    :param ent_names_list: 企业ID
    :param port_names_list: 排口名
    :param specie_names_list: 因子名
    :param check_start_time:
    :param end_time_dt:
    :param monitoring_type
    :return:
    """
    abnormal_str = ['', 'nan', 'None', np.nan]
    # 第一步：筛选出enterprise_id为当前企业
    ind0 = (skipped_data['enterprise_name'].isin(ent_names_list))

    # 第二步：筛选排口和因子
    # unmonitoring_info为企业
    ind1_0 = skipped_data['unmonitoring_info'].isin(['企业', *ent_names_list])

    # unmonitoring_info为监测点、且unmonitoring_site为当前排口
    ind1_1 = skipped_data['unmonitoring_info'].isin(['监测点', '生产设施']) & \
             skipped_data['unmonitoring_site'].isin([*port_names_list, '所有测点'])

    # # unmonitoring_info为监测项目、且unmonitoring_site为当前排口或为空、且unmonitoring_indicator为当前因子
    # ind1_2 = skipped_data['unmonitoring_info'].isin(['监测项目']) & \
    #          skipped_data['unmonitoring_site'].isin([*port_names_list, *abnormal_str, '所有测点']) & \
    #          skipped_data['unmonitoring_indicator'].isin(specie_names_list)

    # unmonitoring_info为污染源类别、且unmonitoring_indicator为当前类别
    ind1_3 = skipped_data['unmonitoring_info'].isin(['污染源类别', '类别']) & \
             skipped_data['unmonitoring_indicator'].str.contains(monitoring_type)

    # ind1 = ind1_0 | ind1_1 | ind1_2 | ind1_3
    ind1 = ind1_0 | ind1_1 | ind1_3
    ind = ind0 & ind1
    ent_port_skipped = skipped_data[ind]

    return ent_port_skipped



if __name__ == '__main__':
    from lib import city_code_pinyin_map

    # Example usage
    province_pinyin = 'chongqing'
    area_code = '500107000'
    city_name = city_code_pinyin_map[area_code]
    start_time = '2025-05-01 00:00:00'
    end_time = '2025-05-15 23:59:59'

    df_map = get_ue_monitor_info_map(province_pinyin, area_code)
    # df_map.columns
    # ['ue_enterprise_id', 'ue_enterprise_name', 'enterprise_id', 
    # 'monitoring_site_data', 'monitoring_site_id', 'line_id', 'line_name']

    df_scheme, all_ent_names_list = request_self_monitor_scheme_from_db(province_pinyin, area_code, df_map)
    # df_scheme.columns
    # ['enterprise_id', 'enterprise_name_list', 'monitoring_site_id', 'specie_id', 
    # 'monitoring_type', 'monitoring_site_list', 'std_name', 'std_type', 
    # 'monitoring_indicator_list', 'ue_enterprise_id', 'ue_enterprise_name', 
    # 'monitoring_site_data', 'line_id', 'line_name']
    
    skipped_data = get_skipped_data(
        area_pinyin = province_pinyin,
        area_code = area_code,
        start_time = start_time,
        end_time = end_time,
        all_ent_names_list = all_ent_names_list
    )
    # skipped_data.columns
    # ['id', 'creat_time', 'region_code', 'self_id', 'enterprise_name',
    #    'unmonitoring_info', 'unmonitoring_site', 'unmonitoring_indicator',
    #    'start_time', 'end_time', 'monitoring_mode', 'whether_stop',
    #    'adding_time', 'unmonitoring_cause', 'monitoring_type']

    df_ue_info, df_ue_data = get_ue_info_and_data(
        province_pinyin = province_pinyin,
        area_code = area_code,
        start_time = start_time,
        end_time = end_time,
        all_ent_names_list = all_ent_names_list
    )

    # df_ue_info.columns
    # ['ue_enterprise_id', 'facility_id', 'category_code', 'line_id', 
    # 'ue_enterprise_name', 'line_name', 'facility_name', 'source', 
    # 'facility_sequence_number', 'rated_power', 'is_gas_related', 
    # 'region_code', 'uscc', 'hist_mean_power', 'pre_nd_mean_power', 
    # 'stat_mean_power', 'use_rated_power', 'reference_power']

    # df_ue_data.columns
    # ['monitor_time_rounded', 'ue_enterprise_id', 'facility_id', 'voltage_a',
    #    'voltage_b', 'voltage_c', 'active_power', 'max_voltage',
    #    'is_long_missing']


    print(df_ue_info.head())
    print(df_ue_data.head())
