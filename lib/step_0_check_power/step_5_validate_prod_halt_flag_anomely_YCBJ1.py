# -*- coding: utf-8 -*-
"""
Created on 2025/7/22 下午3:24

@Project -> File: srv-enterprise-power-usage-verify -> step_4_verify_prod_halt_flag_anomely.py

@Author: vincent-cj

@Describe:
"""
import sys
import numpy as np
import pandas as pd
sys.path.append('../..')
from lib import logger
from lib.step_0_check_power.data_utils import (
    get_ue_monitor_info_map,
    request_self_monitor_scheme_from_db,
    get_skipped_data,
    get_ue_info_and_data
)


def filter_facility_skipped_data(
        skipped_data: pd.DataFrame,
        ent_names_list,
        port_names_list,
        monitoring_type,
        specie_names_list = None
):
    """
    过滤符合当前企业、排污类别、排口的未生产记录
    :param skipped_data: 总停产记录
    :param ent_names_list: 企业ID
    :param port_names_list: 排口名
    :param specie_names_list: 因子名
    :param check_start_time:
    :param end_time_dt:
    :param monitoring_type
    :return:
    """
    abnormal_str = ['', 'nan', 'None', np.nan]
    # 第一步：筛选出enterprise_id为当前企业
    ind0 = (skipped_data['enterprise_name'].isin(ent_names_list))

    # 第二步：筛选排口和因子
    # unmonitoring_info为企业
    ind1_0 = skipped_data['unmonitoring_info'].isin(['企业', *ent_names_list])

    # unmonitoring_info为监测点、且unmonitoring_site为当前排口
    ind1_1 = skipped_data['unmonitoring_info'].isin(['监测点', '生产设施']) & \
             skipped_data['unmonitoring_site'].isin([*port_names_list, '所有测点'])

    # # unmonitoring_info为监测项目、且unmonitoring_site为当前排口或为空、且unmonitoring_indicator为当前因子
    # ind1_2 = skipped_data['unmonitoring_info'].isin(['监测项目']) & \
    #          skipped_data['unmonitoring_site'].isin([*port_names_list, *abnormal_str, '所有测点']) & \
    #          skipped_data['unmonitoring_indicator'].isin(specie_names_list)

    # unmonitoring_info为污染源类别、且unmonitoring_indicator为当前类别
    ind1_3 = skipped_data['unmonitoring_info'].isin(['污染源类别', '类别']) & \
             skipped_data['unmonitoring_indicator'].str.contains(monitoring_type)

    # ind1 = ind1_0 | ind1_1 | ind1_2 | ind1_3
    ind1 = ind1_0 | ind1_1 | ind1_3
    ind = ind0 & ind1
    ent_port_skipped = skipped_data[ind]

    return ent_port_skipped


def detect_production_halt_anomaly(
        df_scheme: pd.DataFrame,
        skipped_data: pd.DataFrame,
        df_ue_info: pd.DataFrame,
        df_ue_data: pd.DataFrame,
        start_dt: pd.Timestamp,
        end_dt: pd.Timestamp,
        min_anomaly_minutes: int = 30,
        prod_rated_ratio: float = 0.5,
        prod_stat_ratio: float = 0.8,
        time_granularity: int = 5
):
    """
    检测生产设施停产标记异常

    参数说明:
    :param df_scheme: 自行监测基础信息DataFrame
    :param skipped_data: 未监测说明数据DataFrame
    :param df_ue_info: 用能设施基础信息DataFrame
    :param df_ue_data: 用能监测数据DataFrame
    :param start_dt: 任务开始时间
    :param end_dt: 任务结束时间
    :param min_anomaly_minutes: 最小异常持续时间（分钟）
    :param prod_rated_ratio: 生产设施额定功率比例系数
    :param prod_stat_ratio: 生产设施统计功率比例系数
    :param time_granularity: 时间粒度（分钟）
    :return: 异常线索列表
    """
    from lib.step_0_check_power.check_utils import merge_time_intervals

    # 数据验证
    if df_scheme.empty or skipped_data.empty or df_ue_info.empty or df_ue_data.empty:
        logger.warning("输入数据为空，跳过异常检测")
        return []

    # 验证必要字段是否存在
    required_scheme_cols = ['enterprise_id', 'ue_enterprise_id', 'ue_enterprise_name',
                           'monitoring_site_data', 'monitoring_site_id', 'monitoring_type',
                           'enterprise_name_list', 'monitoring_site_list', 'line_id', 'line_name']
    required_skip_cols = ['id', 'enterprise_name', 'start_time', 'end_time']
    required_ue_info_cols = ['ue_enterprise_id', 'line_id', 'category_code', 'facility_id',
                            'facility_name', 'reference_power', 'use_rated_power']
    required_ue_data_cols = ['facility_id', 'monitor_time_rounded', 'active_power']

    for col in required_scheme_cols:
        if col not in df_scheme.columns:
            logger.error(f"df_scheme缺少必要字段: {col}")
            return []

    for col in required_skip_cols:
        if col not in skipped_data.columns:
            logger.error(f"skipped_data缺少必要字段: {col}")
            return []

    for col in required_ue_info_cols:
        if col not in df_ue_info.columns:
            logger.error(f"df_ue_info缺少必要字段: {col}")
            return []

    for col in required_ue_data_cols:
        if col not in df_ue_data.columns:
            logger.error(f"df_ue_data缺少必要字段: {col}")
            return []

    anomaly_clues = []

    # 按排口循环处理
    for _, scheme_row in df_scheme.iterrows():
        try:
            # 获取企业和排口信息
            enterprise_id = scheme_row['enterprise_id']
            ue_enterprise_id = scheme_row['ue_enterprise_id']
            ue_enterprise_name = scheme_row['ue_enterprise_name']
            monitoring_site_data = scheme_row['monitoring_site_data']
            monitoring_site_id = scheme_row['monitoring_site_id']
            monitoring_type = scheme_row['monitoring_type']

            # 获取企业别名列表和排口别名列表
            ent_names_list = scheme_row['enterprise_name_list']
            port_names_list = scheme_row['monitoring_site_list']

            # 筛选与该排口相关的停产数据
            facility_skipped = filter_facility_skipped_data(
                skipped_data, ent_names_list, port_names_list, monitoring_type
            )

            if facility_skipped.empty:
                continue

            # 准备时间段和ID的列表用于聚合
            time_intervals_with_ids = []
            for _, skip_row in facility_skipped.iterrows():
                start_time = pd.to_datetime(skip_row['start_time'])
                end_time = pd.to_datetime(skip_row['end_time'])
                skip_data_id = skip_row['id']
                time_intervals_with_ids.append((start_time, end_time, [skip_data_id]))

            # 聚合重叠的时间段
            merged_intervals = merge_time_intervals(time_intervals_with_ids)

            if not merged_intervals:
                continue

            # 获取该排口对应的生产线
            line_id = scheme_row['line_id']
            line_name = scheme_row['line_name']

            # 获取该生产线对应的所有生产设施
            production_facilities = df_ue_info[
                (df_ue_info['ue_enterprise_id'] == ue_enterprise_id) &
                (df_ue_info['line_id'] == line_id) &
                (df_ue_info['category_code'] == 'PRODUCTION_LINK')
            ]

            if production_facilities.empty:
                continue

            # 对每个生产设施进行异常检测
            for _, facility in production_facilities.iterrows():
                facility_id = facility['facility_id']
                facility_name = facility['facility_name']
                reference_power = facility['reference_power']
                use_rated_power = facility['use_rated_power']

                # 检查参考功率是否有效
                if pd.isna(reference_power) or reference_power <= 0:
                    continue

                # 计算停产阈值
                if use_rated_power:
                    threshold = reference_power * prod_rated_ratio
                else:
                    threshold = reference_power * prod_stat_ratio

                # 获取该设施的监测数据
                facility_data = df_ue_data[df_ue_data['facility_id'] == facility_id].copy()
                if facility_data.empty:
                    continue

                # 对每个聚合后的停产时间段进行检测
                for interval_start, interval_end, skip_data_ids in merged_intervals:
                    # 筛选停产时间段内的监测数据
                    interval_data = facility_data[
                        (facility_data['monitor_time_rounded'] >= interval_start) &
                        (facility_data['monitor_time_rounded'] <= interval_end)
                    ].copy()

                    if interval_data.empty:
                        continue

                    # 过滤掉长时间缺失的数据
                    if 'is_long_missing' in interval_data.columns:
                        interval_data = interval_data[interval_data['is_long_missing'] != True]

                    if interval_data.empty:
                        continue

                    # 判断设施运行状态（功率大于等于阈值为开机）
                    interval_data['is_running'] = interval_data['active_power'] >= threshold

                    # 检测连续开机的时间段
                    running_periods = []
                    current_start = None
                    current_end = None

                    # 按时间排序
                    interval_data = interval_data.sort_values('monitor_time_rounded')

                    for _, row in interval_data.iterrows():
                        if row['is_running']:
                            if current_start is None:
                                current_start = row['monitor_time_rounded']
                            current_end = row['monitor_time_rounded']
                        else:
                            if current_start is not None:
                                running_periods.append((current_start, current_end))
                                current_start = None
                                current_end = None

                    # 处理最后一个开机时段
                    if current_start is not None:
                        running_periods.append((current_start, current_end))

                    # 检查是否存在持续时间超过阈值的开机时段
                    for period_start, period_end in running_periods:
                        # 验证时间段有效性
                        if pd.isna(period_start) or pd.isna(period_end) or period_start >= period_end:
                            continue

                        duration_minutes = (period_end - period_start).total_seconds() / 60

                        if duration_minutes >= min_anomaly_minutes:
                            # 计算该时段的平均功率
                            period_data = interval_data[
                                (interval_data['monitor_time_rounded'] >= period_start) &
                                (interval_data['monitor_time_rounded'] <= period_end)
                            ]

                            if period_data.empty:
                                logger.warning(f"时段 {period_start} 到 {period_end} 无有效数据")
                                continue

                            avg_power = period_data['active_power'].mean()

                            # 验证平均功率有效性
                            if pd.isna(avg_power):
                                logger.warning(f"设施 {facility_id} 在时段 {period_start} 到 {period_end} 平均功率无效")
                                continue

                            # 生成异常线索
                            anomaly_clue = {
                                'ue_enterprise_id': ue_enterprise_id,
                                'ue_enterprise_name': ue_enterprise_name,
                                'enterprise_id': enterprise_id,
                                'line_id': line_id,
                                'line_name': line_name,
                                'monitoring_site_data': monitoring_site_data,
                                'monitoring_site_id': monitoring_site_id,
                                'facility_id': facility_id,
                                'facility_name': facility_name,
                                'skip_data_ids': skip_data_ids,
                                'alert_start_time': period_start.strftime('%Y-%m-%d %H:%M:%S'),
                                'alert_end_time': period_end.strftime('%Y-%m-%d %H:%M:%S'),
                                'threshold': round(threshold, 2),
                                'avg_power': round(avg_power, 2),
                                'duration_minutes': f"{duration_minutes:.0f}分钟"
                            }

                            anomaly_clues.append(anomaly_clue)

        except Exception as e:
            logger.error(f"处理排口 {scheme_row.get('monitoring_site_data', 'Unknown')} 时发生错误: {e}")
            continue

    return anomaly_clues


def validate_prod_halt_flag_anomaly(
        province_pinyin: str,
        area_code: str,
        start_time: str,
        end_time: str,
        min_anomaly_minutes: int = 30,
        time_granularity: int = 5,
        extension_minutes: int = 30,
        prod_rated_ratio: float = 0.5,
        prod_stat_ratio: float = 0.8,
        is_upload_file: bool = True,
        file_dir: str = None
):
    """
    停产标记异常校验主函数

    参数说明:
    :param province_pinyin: 省份拼音
    :param area_code: 区域代码
    :param start_time: 任务开始时间
    :param end_time: 任务结束时间
    :param min_anomaly_minutes: 最小异常持续时间（分钟）
    :param time_granularity: 时间粒度（分钟）
    :param extension_minutes: 绘图时间扩展（分钟）
    :param prod_rated_ratio: 生产设施额定功率比例系数
    :param prod_stat_ratio: 生产设施统计功率比例系数
    :param is_upload_file: 是否上传图片
    :param file_dir: 图片保存目录
    :return: 异常线索字典，格式为 {"items": [...], "num": int}
    """
    from lib.step_0_check_power.check_utils import draw_and_post_img
    from lib import is_upload_file as global_is_upload_file, proj_dir
    import os

    try:
        # 设置默认参数
        if file_dir is None:
            file_dir = f"{proj_dir}/temp_images"

        # 确保目录存在
        os.makedirs(file_dir, exist_ok=True)

        # 转换时间格式
        start_dt = pd.to_datetime(start_time)
        end_dt = pd.to_datetime(end_time)

        # 为避免漏掉跨任务的异常线索，将起始时间前移
        extended_start_dt = start_dt - pd.DateOffset(minutes=min_anomaly_minutes - time_granularity)

        logger.info(f"开始停产标记异常校验，时间范围：{extended_start_dt} 到 {end_dt}")

        # 获取数据源映射关系
        df_map = get_ue_monitor_info_map(province_pinyin, area_code)
        if df_map.empty:
            logger.warning("未获取到数据源映射关系")
            return {"items": [], "num": 0}

        # 获取自行监测基础信息
        df_scheme, all_ent_names_list = request_self_monitor_scheme_from_db(province_pinyin, area_code, df_map)
        if df_scheme.empty:
            logger.warning("未获取到自行监测基础信息")
            return {"items": [], "num": 0}

        # 获取停产数据
        skipped_data = get_skipped_data(
            area_pinyin=province_pinyin,
            area_code=area_code,
            start_time=extended_start_dt.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time,
            all_ent_names_list=all_ent_names_list
        )
        if skipped_data.empty:
            logger.info("未获取到停产数据")
            return {"items": [], "num": 0}

        # 获取用能设施信息和监测数据
        df_ue_info, df_ue_data = get_ue_info_and_data(
            province_pinyin=province_pinyin,
            area_code=area_code,
            start_time=extended_start_dt.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time,
            all_ent_names_list=all_ent_names_list
        )

        if df_ue_info.empty or df_ue_data.empty:
            logger.warning("未获取到用能数据")
            return {"items": [], "num": 0}

        # 执行异常检测
        anomaly_clues = detect_production_halt_anomaly(
            df_scheme=df_scheme,
            skipped_data=skipped_data,
            df_ue_info=df_ue_info,
            df_ue_data=df_ue_data,
            start_dt=extended_start_dt,
            end_dt=end_dt,
            min_anomaly_minutes=min_anomaly_minutes,
            prod_rated_ratio=prod_rated_ratio,
            prod_stat_ratio=prod_stat_ratio,
            time_granularity=time_granularity
        )

        logger.info(f"检测到 {len(anomaly_clues)} 条停产标记异常线索")

        # 为每条异常线索绘制图表
        for clue in anomaly_clues:
            try:
                # 获取设施监测数据用于绘图
                facility_id = clue['facility_id']
                alert_start_time = pd.to_datetime(clue['alert_start_time'])
                alert_end_time = pd.to_datetime(clue['alert_end_time'])

                # 绘图时间范围：异常时间段前后各扩展30分钟
                plot_start_time = alert_start_time - pd.DateOffset(minutes=extension_minutes)
                plot_end_time = alert_end_time + pd.DateOffset(minutes=extension_minutes)

                # 获取绘图数据
                plot_data = df_ue_data[
                    (df_ue_data['facility_id'] == facility_id) &
                    (df_ue_data['monitor_time_rounded'] >= plot_start_time) &
                    (df_ue_data['monitor_time_rounded'] <= plot_end_time)
                ].copy()

                if plot_data.empty:
                    logger.warning(f"设施 {facility_id} 无绘图数据")
                    clue['uploaded_file_info'] = {}
                    continue

                # 准备绘图数据
                plot_data = plot_data.set_index('monitor_time_rounded')
                plot_data = plot_data[['active_power']].sort_index()

                # 生成图片文件名
                start_str = alert_start_time.strftime('%Y-%m-%dT%H:%M:%S')
                end_str = alert_end_time.strftime('%Y-%m-%dT%H:%M:%S')
                pic_path = f"{file_dir}/{clue['ue_enterprise_name']}_" \
                          f"{clue['line_name'] or clue['line_id']}_prod_halt_anomaly_" \
                          f"{start_str}_{end_str}.png"

                # 绘制图表
                uploaded_file_info = draw_and_post_img(
                    df_data=plot_data,
                    plot_fields=['active_power'],
                    field_labels=[clue['facility_name']],
                    title='停产标记异常',
                    xlabel='时间',
                    ylabel='有功功率(kW)',
                    reference_line=clue['threshold'],
                    reference_desc='生产设施参考功率',
                    alert_start_time=alert_start_time,
                    alert_end_time=alert_end_time,
                    is_upload=is_upload_file and global_is_upload_file,
                    pic_path=pic_path
                )

                clue['uploaded_file_info'] = uploaded_file_info

            except Exception as e:
                logger.error(f"为异常线索绘图时发生错误: {e}")
                clue['uploaded_file_info'] = {}

        # 返回结果
        result = {
            "items": anomaly_clues,
            "num": len(anomaly_clues)
        }

        logger.info(f"停产标记异常校验完成，共发现 {len(anomaly_clues)} 条异常线索")
        return result

    except Exception as e:
        logger.error(f"停产标记异常校验过程中发生错误: {e}")
        return {"items": [], "num": 0}



if __name__ == '__main__':
    from lib import city_code_pinyin_map, proj_dir

    # 示例用法：停产标记异常检测
    province_pinyin = 'chongqing'
    area_code = '500107000'
    city_name = city_code_pinyin_map[area_code]
    start_time = '2025-05-01 00:00:00'
    end_time = '2025-05-15 23:59:59'

    # 执行停产标记异常校验
    result = validate_prod_halt_flag_anomaly(
        province_pinyin=province_pinyin,
        area_code=area_code,
        start_time=start_time,
        end_time=end_time,
        min_anomaly_minutes=30,
        time_granularity=5,
        extension_minutes=30,
        prod_rated_ratio=0.5,
        prod_stat_ratio=0.8,
        is_upload_file=True,
        file_dir=f"{proj_dir}/temp_images"
    )

    print(f"检测结果：")
    print(f"异常线索数量：{result['num']}")

    if result['items']:
        print("\n异常线索详情：")
        for i, clue in enumerate(result['items'], 1):
            print(f"\n第{i}条异常线索：")
            print(f"  企业名称：{clue['ue_enterprise_name']}")
            print(f"  产线名称：{clue['line_name']}")
            print(f"  设施名称：{clue['facility_name']}")
            print(f"  异常时间：{clue['alert_start_time']} 至 {clue['alert_end_time']}")
            print(f"  持续时间：{clue['duration_minutes']}")
            print(f"  停产阈值：{clue['threshold']:.2f} kW")
            print(f"  平均功率：{clue['avg_power']:.2f} kW")
            if clue['uploaded_file_info']:
                print(f"  图片链接：{clue['uploaded_file_info'].get('url', '未上传')}")
    else:
        print("未发现停产标记异常")
