"""
#!/usr/bin/env python
#-*-coding: utf-8 -*-

  @ Author: vincent-cj

  @ Date: 2025-07-11 10:07:78 AM

  @ FilePath: -> srv-enterprise-power-usage-verify -> lib -> step_0_check_power -> utils.py

  @ Description: 

"""
import re
import pandas as pd
import requests
import os
from matplotlib import pyplot as plt
from matplotlib import dates as mdates
from matplotlib import font_manager
import sys

sys.path.append('../..')
from lib import logger, upload_file_url, proj_dir

font_path = f'{proj_dir}/static/SimHei.ttf'
font_prop = font_manager.FontProperties(fname = font_path)
plt.rcParams['font.family'] = font_prop.get_name()
plt.rcParams["axes.unicode_minus"] = False  # 正常显示负号


def replace_code_suffix(area_code: str, repl: str = '') -> str:
    """将区域编码最后的连续的0替换为特定的字符"""
    return re.sub(r'0+$', repl, area_code)


def draw_and_post_img(df_data, plot_fields = None, field_labels = None, title = None,
                      xlabel = '时间', ylabel = '监测值', reference_line = None, show_grid = True,
                      is_upload = False, pic_path = None, alert_start_time = None,
                      alert_end_time = None, reference_desc = None) -> dict:
    """
    绘制折线图并推送至指定URL路径

    参数说明:
    :param df_data: pandas DataFrame对象，index为时间格式，columns为需要绘制的字段
    :param plot_fields: 需要绘制的字段列表(list)，为None时使用df_data的全部字段
    :param field_labels: 字段的中文名称列表(list)，与plot_fields数量一致，为None时使用原字段名
    :param title: 图片标题(str)，为None时不添加标题
    :param xlabel: x轴标签(str)，默认为'时间'，为None时不添加标签
    :param ylabel: y轴标签(str)，默认为'监测值'，为None时不添加标签
    :param reference_line: 参考线数值(float)，为None时不绘制参考线
    :param show_grid: 是否显示网格(bool)，默认为True
    :param is_upload: 是否上传图片(bool)，默认为True
    :param pic_path: 图片保存路径(str)，必须提供
    :param alert_start_time: 异常开始时间(pd.Timestamp)，为None时不绘制垂直虚线
    :param alert_end_time: 异常结束时间(pd.Timestamp)，为None时不绘制垂直虚线
    :param reference_desc: 参考线描述(str)，为None时不在图例中显示
    :return: 上传成功时返回图片信息(dict)，上传失败或不上传时返回空字字典
    """

    if pic_path is None:
        raise ValueError("pic_path参数必须提供")

    # 确保保存目录存在
    os.makedirs(os.path.dirname(pic_path), exist_ok = True)

    # 设置中文字体
    # plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC']
    # plt.rcParams['axes.unicode_minus'] = False

    uploaded_file_info = {}

    try:
        # 处理数据
        if plot_fields is None:
            plot_fields = df_data.columns.tolist()
        else:
            # 验证plot_fields是df_data的子集
            invalid_fields = [f for f in plot_fields if f not in df_data.columns]
            if invalid_fields:
                raise ValueError(f"以下字段不存在于df_data中: {invalid_fields}")

        # 验证field_labels与plot_fields数量一致
        if field_labels is not None and len(field_labels) != len(plot_fields):
            raise ValueError("field_labels与plot_fields数量必须一致")

        # 使用field_labels或原字段名作为图例
        legend_labels = field_labels if field_labels is not None else plot_fields

        # 准备绘图数据
        # plot_data = df_data[plot_fields].copy()
        plot_data = df_data[plot_fields]
        plot_data = plot_data.sort_index(ascending = True)

        # 创建图形
        fig, ax = plt.subplots(figsize = (12, 7), dpi = 300)

        # 设置标题
        if title is not None:
            ax.set_title(str(title), fontsize = 14, fontweight = 'bold', )

        # 绘制折线
        for i, (field, label) in enumerate(zip(plot_fields, legend_labels)):
            ax.plot(plot_data.index, plot_data[field],
                    label = str(label), linewidth = 2, marker = 'o', markersize = 3)

        # 添加参考线
        if reference_line is not None:
            reference_label = reference_desc if reference_desc is not None else None
            ax.axhline(y = float(reference_line), color = 'red', linestyle = '--',
                       linewidth = 1.5, alpha = 0.5, label = reference_label)

        # 添加异常时间段标记的垂直虚线
        if alert_start_time is not None:
            ax.axvline(x = alert_start_time, color = 'orange', linestyle = '--',
                       linewidth = 1.5, alpha = 0.5)
        if alert_end_time is not None:
            ax.axvline(x = alert_end_time, color = 'orange', linestyle = '--',
                       linewidth = 1.5, alpha = 0.5)

        # 设置坐标轴标签
        if xlabel is not None:
            ax.set_xlabel(str(xlabel), fontsize = 12, fontproperties=font_prop)
        if ylabel is not None:
            ax.set_ylabel(str(ylabel), fontsize = 12, fontproperties=font_prop)

        # 设置网格
        if show_grid:
            ax.grid(True, alpha = 0.3, linestyle = '-', linewidth = 0.2)

        # 设置图例
        ax.legend(fontsize = 10, loc = 'best', prop=font_prop)

        # 设置时间格式
        if pd.api.types.is_datetime64_any_dtype(plot_data.index):
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d %H:%M'))
            # plt.xticks(rotation = 45, ha = 'right')
            plt.gcf().autofmt_xdate()  # 自动旋转日期标签
        # 调整布局
        plt.tight_layout()

        # 保存到本地
        plt.savefig(pic_path, format = 'png', dpi = 300, bbox_inches = 'tight')
        logger.info(f"图像已保存到: {pic_path}")

        # 上传图片
        if is_upload:
            try:
                with open(pic_path, 'rb') as f:
                    files = {
                        'file': (os.path.basename(pic_path), f, 'image/png')
                    }
                    response = requests.post(upload_file_url, files = files, timeout = 30)
                    response.raise_for_status()

                    response_data = response.json()
                    if 'data' in response_data and 'url' in response_data['data']:
                        uploaded_file_info = response_data['data']
                        img_url = uploaded_file_info['url']
                        logger.info(f"图片{os.path.basename(pic_path)}上传成功: {img_url}")
                    else:
                        logger.warning(f"图片{os.path.basename(pic_path)}上传响应格式异常: {response_data}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"图片{os.path.basename(pic_path)}上传失败: {e}")
            except Exception as e:
                logger.warning(f"图片{os.path.basename(pic_path)}上传异常: {e}")

    except Exception as e:
        logger.error(f"图片{os.path.basename(pic_path)}绘制过程发生错误: {e}")

    finally:
        plt.close('all')

    return uploaded_file_info


def judge_facility_status(
        facilities: pd.DataFrame,
        data: pd.DataFrame,
        prod_rated_ratio: float = 0.5,
        prod_stat_ratio: float = 0.8,
        gov_rated_ratio: float = 0.1,
        gov_stat_ratio: float = 0.2,
        is_production = True,
        pro_long_miss_stat: bool = True
):
    """
    判断设施运行状态
    """
    facility_status = {}

    for _, facility in facilities.iterrows():
        facility_data = data[data['facility_id'] == facility['facility_id']]
        if facility_data.empty:
            facility_status[facility['facility_id']] = pd.Series(dtype = bool)
            continue

        # 获取参考功率和阈值
        ref_power = facility['reference_power']
        if pd.isna(ref_power) or ref_power <= 0:
            facility_status[facility['facility_id']] = pd.Series(dtype = bool)
            continue

        if is_production:
            # 生产设施：实时功率 >= 统计均值80% 或 额定功率50%
            if facility['use_rated_power']:
                threshold = facility['rated_power'] * prod_rated_ratio
            else:
                threshold = ref_power * prod_stat_ratio
        else:
            # 治理设施：实时功率 >= 额定功率10% 或 统计均值20%
            if facility['use_rated_power']:
                threshold = facility['rated_power'] * gov_rated_ratio
            else:
                threshold = ref_power * gov_stat_ratio

        # 判断运行状态
        status = facility_data['active_power'] >= threshold
        status.index = facility_data['monitor_time_rounded']

        # 对于生产设施，将长时间缺失的时段状态置为特定值
        # （如：校验治理设施是否违规停运时为True,校验生产设施是否违规运行时为False）
        if is_production and 'is_long_missing' in facility_data.columns:
            long_missing_mask = facility_data['is_long_missing'] == pro_long_miss_stat
            if long_missing_mask.any():
                missing_times = facility_data.loc[long_missing_mask, 'monitor_time_rounded']
                status[missing_times] = False

        facility_status[facility['facility_id']] = status

    return facility_status


def merge_time_intervals(time_intervals_with_ids):
    """
    合并重叠或相邻的时间段，并保留对应的数据ID

    参数说明:
    :param time_intervals_with_ids: 包含时间段和ID的列表，格式为[(start_time, end_time, [id_list]), ...]
    :return: 合并后的时间段列表，格式为[(start_time, end_time, [merged_id_list]), ...]
    """
    if not time_intervals_with_ids:
        return []

    # 按开始时间排序
    sorted_intervals = sorted(time_intervals_with_ids, key=lambda x: x[0])

    merged = []
    current_start, current_end, current_ids = sorted_intervals[0]

    for start, end, ids in sorted_intervals[1:]:
        # 如果当前时间段与下一个时间段重叠或相邻（允许5分钟的间隔）
        if start <= current_end + pd.Timedelta(minutes=5):
            # 合并时间段
            current_end = max(current_end, end)
            # 合并ID列表
            if isinstance(current_ids, list):
                current_ids.extend(ids if isinstance(ids, list) else [ids])
            else:
                current_ids = [current_ids] + (ids if isinstance(ids, list) else [ids])
        else:
            # 保存当前时间段
            merged.append((current_start, current_end, current_ids))
            # 开始新的时间段
            current_start, current_end, current_ids = start, end, ids

    # 添加最后一个时间段
    merged.append((current_start, current_end, current_ids))

    return merged


def test_merge_time_intervals():
    """
    测试时间聚合函数的正确性
    """
    import pandas as pd

    # 测试用例1：重叠时间段
    test_data_1 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:30:00'), ['id1']),
        (pd.Timestamp('2025-01-01 10:25:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id2']),
        (pd.Timestamp('2025-01-01 11:00:00'), pd.Timestamp('2025-01-01 11:15:00'), ['id3'])
    ]

    result_1 = merge_time_intervals(test_data_1)
    expected_1 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id1', 'id2']),
        (pd.Timestamp('2025-01-01 11:00:00'), pd.Timestamp('2025-01-01 11:15:00'), ['id3'])
    ]

    print("测试用例1 - 重叠时间段:")
    print(f"输入: {test_data_1}")
    print(f"输出: {result_1}")
    print(f"预期: {expected_1}")
    print(f"通过: {len(result_1) == 2 and result_1[0][0] == expected_1[0][0] and result_1[0][1] == expected_1[0][1]}")

    # 测试用例2：相邻时间段（5分钟内）
    test_data_2 = [
        (pd.Timestamp('2025-01-01 10:00:00'), pd.Timestamp('2025-01-01 10:30:00'), ['id1']),
        (pd.Timestamp('2025-01-01 10:33:00'), pd.Timestamp('2025-01-01 10:45:00'), ['id2'])
    ]

    result_2 = merge_time_intervals(test_data_2)
    print("\n测试用例2 - 相邻时间段:")
    print(f"输入: {test_data_2}")
    print(f"输出: {result_2}")
    print(f"通过: {len(result_2) == 1}")

    # 测试用例3：空输入
    result_3 = merge_time_intervals([])
    print(f"\n测试用例3 - 空输入: {result_3 == []}")

    return True


if __name__ == "__main__":
    # 运行时间聚合测试
    test_merge_time_intervals()
