"""
#!/usr/bin/env python
#-*-coding: utf-8 -*-

  @ Author: vincent-cj

  @ Date: 2025-07-14 09:07:55 AM

  @ FilePath: -> srv-enterprise-power-usage-verify -> lib -> step_0_check_power -> step_0_verify_power_usage.py

  @ Description: 

"""
import sys
import numpy as np
import pandas as pd
import warnings

sys.path.append('../..')

from lib import logger, logging_decorator, is_upload_file
from lib.step_0_check_power.data_utils import get_ue_info_and_data
from lib.step_0_check_power.check_utils import draw_and_post_img, judge_facility_status

warnings.filterwarnings("ignore")


def judge_governance_facilities_status(
        governance_facilities: pd.DataFrame,
        data: pd.DataFrame,
        gov_rated_ratio: float = 0.1,
        gov_stat_ratio: float = 0.2
):
    """先按facility_sequence_number分组，再判断并行/串行状态"""
    # 按facility_sequence_number分组
    seq_groups = governance_facilities.groupby('facility_sequence_number')
    group_status = []

    for seq_num, seq_facilities in seq_groups:
        if pd.isna(seq_num):
            # 没有sequence_number的设施单独处理
            for _, facility in seq_facilities.iterrows():
                facility_data = data[data['facility_id'] == facility['facility_id']]
                if facility_data.empty:
                    continue

                # 获取参考功率和阈值
                ref_power = facility['reference_power']
                if pd.isna(ref_power) or ref_power <= 0:
                    continue

                # 单个设施判断
                if facility['use_rated_power']:
                    threshold = facility['rated_power'] * gov_rated_ratio
                else:
                    threshold = ref_power * gov_stat_ratio

                status = facility_data['active_power'] >= threshold
                status.index = facility_data['monitor_time_rounded']

                # 对于治理设施，将长时间缺失的时段状态置为True
                if 'is_long_missing' in facility_data.columns:
                    long_missing_mask = facility_data['is_long_missing'] == True
                    if long_missing_mask.any():
                        missing_times = facility_data.loc[long_missing_mask, 'monitor_time_rounded']
                        status[missing_times] = True

                group_status.append(status)
        else:
            # 相同sequence_number为并行，需要考虑功率加和
            # 获取所有并行设施的数据
            parallel_data_list = []
            refer_power_list = []
            use_rated_power_map = {}
            time_idx = None

            for _, facility in seq_facilities.iterrows():
                facility_data = data[data['facility_id'] == facility['facility_id']].reset_index(drop = True)
                # 数据为空的设施不校验，数据为0且前步骤筛选判断电压>=200的说明为有效数据，需要校验且判定为未正常工作
                # 额定或统计功率为0的不校验
                ref_power = facility['reference_power']
                if pd.isna(ref_power) or ref_power <= 0:
                    continue
                if facility_data.empty:
                    continue

                if time_idx is None:
                    time_idx = facility_data['monitor_time_rounded']
                # 统计需要校验的设施的相关数据
                parallel_data_list.append(facility_data[['active_power']])

                # 累计额定功率或统计功率
                refer_power_list.append(ref_power)
                use_rated_power_map[facility['facility_id']] = facility['use_rated_power']

            # 数据为空或rated_power为0的不校验，状态置为正常
            if len(parallel_data_list) == 0 or sum(refer_power_list) == 0:
                # group_status.append(pd.Series([True] * len(data)))
                continue

            use_rated_power = all(use_rated_power_map.values())
            parallel_data = pd.concat(parallel_data_list, axis = 1)

            # 这里的refer_power_list是一个列表，包含了每个设施的参考功率
            refer_power_df = pd.DataFrame([refer_power_list] * len(parallel_data))

            # 对于治理设施，长时间缺失的(nan)时段不进行校验，把对应的参考功率置为nan
            mask = parallel_data.isna()
            refer_power_df[mask.values] = np.nan

            sum_data = pd.Series(np.nansum(parallel_data, axis = 1), index = time_idx)
            sum_threshold = pd.Series(np.nansum(refer_power_df, axis = 1), index = time_idx)
            threshold = sum_threshold * gov_rated_ratio if use_rated_power else sum_threshold * gov_stat_ratio

            parallel_status = sum_data >= threshold
            # parallel_status = pd.Series(parallel_status, index = time_idx)
            group_status.append(parallel_status)

    # 串行关系：所有组都正常才算正常
    if group_status:
        return pd.concat(group_status, axis = 1).all(axis = 1)
    else:
        return None


def get_facilities_str(facilities: pd.DataFrame) -> tuple[str, str]:
    """
    将设施ID与名称转换成字符串，根据facility_sequence_number字段判断设施物理状态
    其中序号相同的代码并行状态，将设施id (facility_id)或设施名称 (facility_name)用`&|&`进行连接
    序号不同的代码串行状态，将设施id (facility_id)或设施名称 (facility_name) 用`&>&`进行连接
    
    Args:
        facilities (pd.DataFrame): 设施信息DataFrame，需包含facility_id、facility_name、facility_sequence_number列
        
    Returns:
        tuple[str, str]: 返回(设施ID字符串, 设施名称字符串)两个字符串
    """
    if facilities.empty:
        return "", ""

    # 按facility_sequence_number分组
    grouped = facilities.groupby('facility_sequence_number')

    # 为每个组创建ID和名称字符串
    id_group_strings = []
    name_group_strings = []

    for seq_num, group in grouped:
        # 组内设施并行连接
        group_id_str = "&|&".join(group['facility_id'].astype(str))
        group_name_str = "&|&".join(group['facility_name'].astype(str))

        id_group_strings.append(group_id_str)
        name_group_strings.append(group_name_str)

    # 不同组串行连接
    ids_result = "&>&".join(id_group_strings)
    names_result = "&>&".join(name_group_strings)

    return ids_result, names_result


@logging_decorator
def get_gov_facility_anomalies(
        df_ue_info: pd.DataFrame,
        df_ue_data: pd.DataFrame,
        start_time: str,
        end_time: str,
        file_dir: str,
        time_granularity: int = 5,
        prod_rated_ratio: float = 0.5,
        prod_stat_ratio: float = 0.8,
        gov_rated_ratio: float = 0.1,
        gov_stat_ratio: float = 0.2,
        min_anomaly_minutes: int = 30,
        extension_minutes: int = 30
) -> list:
    """
    线索1：生产设施正常运行情况下，治理设施未正常运行
    - 对df_info中企业的每个产线进行循环，判断产线下的治理设施是否正常运行。每个产线包括多个设施，设施分为两个类别，以category_code字段进行标识，生成设施为PRODUCTION_LINK，治理设施为GOVERNANCE_LINK。
    - 每个生产线中，需要判断生产与治理设施是否同时存在，如果不同时存在，则不做异常与否的校验。每个生产线中，生成设施与治理设施都可能存在多个。异常校验的核心逻辑是，找出产线中生产设施正常运行情况下，治理设施未正常运行的情况，并列举所有治理设施及其相关信息。
    - 判断生产设施正常运行的条件是：生产设施的实时功率在正常生产时有功功率统计均值80%以上或 额定功率50%以上。污染治理设施未正常运行的判断标准为：治理设施的实时功率＜额定功率的10%，或＜正常治污时有功功率统计均值 20%以下。其中部分设施的额定功率rated_power大于0，当有额定功率时，判断生成设施或治理设施是否正常运行，用额定功率进行判断；如果没有额定功率，就用有功功率的统计值进行判断。统计均值max(历史统计均值，任务开始前五日统计均值），其中历史统计均值来自hist_desc的mean_power字段，任务开始前五日统计均值的计算，从df_data中截取active_power在start_time之前5天的值，取np.nanmean。
    - 同一产线下，如果有多个生产设施，有一个设备正常运行，则认为生产设施为正常运行；如果同一生产线下有多个治理治理设施 存在多个设施，需要通过facility_sequence_number进行判断多个治理设施的物理连接状态，如果facility_sequence_number相同，则说明是并行状态，并行取加和值得到实际运行总功率值，以之与对应设备的额定功率和*10（或统计均值之和 * 20%）进行对比判断，；如果facility_sequence_number不同，则认为是串行状态，存在多个设施串行时，某一设施未开启即认为异常。
    - 异常线索的输出条件为：生产设施正常运行，治污设施未同步开启超过30分钟。df_data中monitor_time字段是5分钟级数据，即原本是将 00，01，02，03，04这五分钟数据取均值得到00分钟这5分钟粒度的数据，同理将 06，07，08，09，10这五分钟数据取均值得到05分钟这5分钟粒度的数据。你需要做几件事情：将不是5分钟粒度的数据圆整到对应五分钟粒度的数据；对于时间戳不缺失、或者'active_power', 'voltage_a', 'voltage_b', 'voltage_c'着几个字段为np.nan的进行插值处理，对于连续缺失达到30分钟的不进行校验、也不必进行插值处理了。对于active_power小于0.5的数据行，需要判断'voltage_a', 'voltage_b', 'voltage_c'中的最大值是否大于等于200，如果小于200，说明传感器坏了，这样的数据行也是无效数据，需要提出，然后按上述要求进行插值与否的判断。
    - 仅对start_time于end_time区间内的数据进行校验。
    - 以一个列表输出所有的异常线索，每个异常线索为一个字段，需要包含如下字段：区域编码、企业名称、企业id、东西部企业分类、产线名称、产线id、生产设施ID、生产设施名称、治理设施ID、治理设施名称、报警开始时间、持续时长。

    Args:
        df_ue_info (pd.DataFrame): Enterprise facility information.
        df_ue_data (pd.DataFrame): Monitoring data.
        start_time (str): Task start time in 'YYYY-MM-DD HH:MM:SS' format.
        end_time (str): Task end time in 'YYYY-MM-DD HH:MM:SS' format.
        file_dir (str): cache dir of the image file.
        time_granularity (int): Time granularity in minutes. Default is 5.
        prod_rated_ratio (float): Rated power ratio for production facilities. Default is 0.5.
        prod_stat_ratio (float): Statistical mean power ratio for production facilities. Default is 0.8.
        gov_rated_ratio (float): Rated power ratio for governance facilities. Default is 0.1.
        gov_stat_ratio (float): Statistical mean power ratio for governance facilities. Default is 0.2.
        min_anomaly_minutes (int): Minimum duration in minutes for an anomaly to be considered valid. Default is 30.
        extension_minutes (int): Minutes to extend the time range for data extraction. Default is 30.

    Returns:
        list: List of anomaly clues.
    """
    logger.info("Starting anomaly detection for government facilities...")
    
    # 为了避免漏掉跨前后两次任务的异常线索，将本次任务的起始时间往前扩展异常线索的最小持续时间
    start_dt = pd.to_datetime(start_time) - pd.DateOffset(minutes = min_anomaly_minutes - time_granularity)
    end_dt = pd.to_datetime(end_time)
    # 校验和绘图，前后各扩展30分钟
    extended_start_time = start_dt - pd.DateOffset(minutes = extension_minutes)
    extended_end_time = end_dt + pd.DateOffset(minutes = extension_minutes)

    # ---------------- 按产线分组处理 ----------------
    anomaly_clues = []
    task_data = df_ue_data[(df_ue_data['monitor_time_rounded'] >= extended_start_time) &
                           (df_ue_data['monitor_time_rounded'] <= extended_end_time)]

    # 按企业id和产线id分组循环
    for (enterprise_id, line_id), line_facilities in df_ue_info.groupby(['ue_enterprise_id', 'line_id']):
        # 检查是否同时包含生产和治理设施
        categories = line_facilities['category_code'].unique()
        if not {'PRODUCTION_LINK', 'GOVERNANCE_LINK'}.issubset(categories):
            continue

        production_facilities = line_facilities[line_facilities['category_code'] == 'PRODUCTION_LINK']
        governance_facilities = line_facilities[line_facilities['category_code'] == 'GOVERNANCE_LINK']
        gov_max_refer = np.nanmax(governance_facilities['reference_power'])
        if np.isnan(gov_max_refer):
            gov_max_refer = None

        # 从line_facilities中获取该企业所有设施id与设施名称的映射字典
        facility_name_map = dict(zip(line_facilities['facility_id'], line_facilities['facility_name']))

        # 获取该企业产线的监测数据
        line_data = task_data[task_data['facility_id'].isin(line_facilities['facility_id'])]

        if line_data.empty:
            continue

        # Pivot the data to have facility_ids as columns and monitor_time_rounded as index
        active_power_df = line_data.pivot(index = 'monitor_time_rounded',
                                          columns = 'facility_id',
                                          values = 'active_power')
        facility_names = [facility_name_map[col] for col in active_power_df.columns if col in facility_name_map]

        # # 绘图所用的数据，需要前后各扩展30分钟
        # active_power_df = active_power_df[(active_power_df.index >= extended_start_time) & 
        #                                  (active_power_df.index <= extended_end_time)]

        # 异常校验：只处理任务时间范围内的数据
        line_data = line_data[(line_data['monitor_time_rounded'] >= start_dt) &
                              (line_data['monitor_time_rounded'] <= end_dt)]
        # 判断生产设施状态
        prod_status = judge_facility_status(production_facilities, line_data, prod_rated_ratio,
                                            prod_stat_ratio, gov_rated_ratio, gov_stat_ratio,
                                            is_production = True)

        # 判断治理设施状态
        gov_overall_status = judge_governance_facilities_status(governance_facilities, line_data,
                                                                gov_rated_ratio, gov_stat_ratio)

        # 生产设施：任一正常即认为整条线正常
        prod_overall_status = None
        if prod_status:
            all_prod_status = pd.concat(prod_status.values(), axis = 1)
            prod_overall_status = all_prod_status.any(axis = 1)  # 任一设施正常即可

        # 检测异常时段
        if prod_overall_status is not None and gov_overall_status is not None:
            # 识别异常时段：生产正常但治理异常
            anomaly_mask: pd.Series = prod_overall_status & (~gov_overall_status)

            # 识别anomaly_mask为True且连续达到30分钟的区间
            if not anomaly_mask.any():
                continue

            # 生成分组标记：状态变化或时间不连续时创建新组
            status_change = (anomaly_mask != anomaly_mask.shift(1))  # 状态变化点
            group_labels = status_change.cumsum()  # 生成分组标签
            anomaly_status = pd.DataFrame([group_labels], index = ['anomaly_status']).T

            # 计算每个异常组的持续时间
            anomaly_groups = anomaly_status[anomaly_mask].groupby('anomaly_status').size() * time_granularity  # 5分钟粒度
            long_anomaly_groups = anomaly_groups[anomaly_groups >= min_anomaly_minutes].index

            for group_num in long_anomaly_groups:
                group_member = anomaly_status[anomaly_status['anomaly_status'] == group_num]
                start_time_anomaly = group_member.index[0]
                end_time_anomaly = group_member.index[-1]
                duration = (end_time_anomaly - start_time_anomaly).total_seconds() / 60 + time_granularity

                # Extend the start and end times for the anomaly group
                extended_start_time_anomaly = max(start_time_anomaly - pd.DateOffset(minutes = extension_minutes),
                                                  extended_start_time)
                extended_end_time_anomaly = min(end_time_anomaly + pd.DateOffset(minutes = extension_minutes),
                                                extended_end_time)

                # Extract the relevant active_power data for the current anomaly group with extended times
                group_active_power = active_power_df.loc[extended_start_time_anomaly:extended_end_time_anomaly]

                # 生成异常线索
                line_info = line_facilities.iloc[0]
                production_facility_ids, production_facility_names = get_facilities_str(production_facilities)
                governance_facility_ids, governance_facility_names = get_facilities_str(governance_facilities)
                clue = {
                    'region_code': line_info['region_code'],
                    'enterprise_name': line_info['ue_enterprise_name'],
                    'enterprise_id': line_info['ue_enterprise_id'],
                    'enterprise_source': line_info['source'],
                    'line_name': line_info['line_name'],
                    'line_id': line_info['line_id'],
                    'production_facility_ids': production_facility_ids,
                    'production_facility_names': production_facility_names,
                    'governance_facility_id': governance_facility_ids,
                    'governance_facility_name': governance_facility_names,
                    'alert_start_time': start_time_anomaly.strftime('%Y-%m-%d %H:%M:%S'),
                    'alert_end_time': end_time_anomaly.strftime('%Y-%m-%d %H:%M:%S'),
                    'duration_minutes': f"{duration:.0f}分钟"
                }

                uploaded_file_info = {}
                if is_upload_file:
                    start_ = start_time_anomaly.strftime('%Y-%m-%dT%H:%M:%S')
                    end_ = end_time_anomaly.strftime('%Y-%m-%dT%H:%M:%S')
                    pic_path = f"{file_dir}/{clue['enterprise_name']}_" \
                               f"{clue['line_name'] or line_id}_gov_facility_anomaly_" \
                               f"{start_}_{end_}.png"
                    uploaded_file_info = draw_and_post_img(
                        group_active_power, field_labels = facility_names,
                        title = '治理设施未正常运行', is_upload = is_upload_file,
                        pic_path = pic_path, reference_line = gov_max_refer
                    )
                clue['uploaded_file_info'] = uploaded_file_info
                anomaly_clues.append(clue)

    gov_facility_anomaly = {
        'items': anomaly_clues,
        'num': len(anomaly_clues),
    }
    return gov_facility_anomaly


if __name__ == '__main__':
    from mod.tool.s3_check_md5 import get_md5_from_bytesOrStr
    from lib import city_code_pinyin_map, cached_dir

    # Example usage
    province_pinyin = 'chongqing'
    area_code = '500107000'
    city_name = city_code_pinyin_map[area_code]
    start_time = '2025-05-01 00:00:00'
    end_time = '2025-05-15 23:59:59'
    # end_time = '2025-05-31 23:59:59'

    prec = 10
    time_flag = f"{start_time[:prec].replace('-', '')}_{end_time[:prec].replace('-', '')}"
    file_tag = f'{area_code}_{time_flag}'
    task_id = get_md5_from_bytesOrStr(file_tag)
    file_name_template = f'{province_pinyin}_{city_name}_{area_code}_{task_id}_' + '{name}' + f'_{time_flag}.' + '{suffix}'
    file_name = file_name_template.format(name = 'abnormal', suffix = 'json')
    file_dir = f'{cached_dir}/ue/{province_pinyin}_{city_name}_{task_id}_{time_flag}/'

    df_info, df_data = get_ue_info_and_data(province_pinyin, area_code, start_time, end_time)

    clues = get_gov_facility_anomalies(df_info, df_data, start_time, end_time, file_dir)
