# -*- coding: utf-8 -*-
"""
Created on 2025/7/4 下午4:49

@Project -> File: srv-enterprise-power-usage-verify -> extract_power_usage_statistics.py

@Author: vincent-cj

@Describe:
"""
import sys
import numpy as np
import pandas as pd
sys.path.append('../..')

from lib import proj_dir, database_conf
from mod.database.query_postgre_table import query_postgre_table
from lib.step_0_check_power.check_utils import replace_code_suffix


def extract_west_data():
    """
    Extracts power usage statistics for the west region.
    """
    # Placeholder for actual extraction logic
    print("Extracting power usage statistics for the west region...")

    ent_info = pd.read_csv(f"{proj_dir}/data/raw_data/west/ent_enterprise.csv")
    pms_info = pd.read_csv(f"{proj_dir}/data/raw_data/west/pms_point.csv")
    power_usage = pd.read_csv(f"{proj_dir}/data/raw_data/west/iot_consume_power_data_202506.csv")

    # 检查 power_usage 中 code 与 point_id 是否一一对应，筛选出一对多或多对一的情况
    code_to_point = power_usage.groupby('code')['point_id'].nunique().reset_index()
    code_to_point = code_to_point[code_to_point['point_id'] > 1]

    point_to_code = power_usage.groupby('point_id')['code'].nunique().reset_index()
    point_to_code = point_to_code[point_to_code['code'] > 1]

    if not code_to_point.empty:
        print("以下 code 对应多个 point_id：")
        print(code_to_point)
    if not point_to_code.empty:
        print("以下 point_id 对应多个 code：")
        print(point_to_code)

    # 合并企业与设备的基础信息
    site_info = pd.merge(ent_info[['id', 'name']].rename(columns = {'id': 'ue_enterprise_id',
                                                                    'name': 'ue_enterprise_name'}),
                         (pms_info[['id', 'name', 'enterprise_id']].
                          rename(columns = {'id': 'point_id',
                                            'name': 'site_name',
                                            'enterprise_id': 'ue_enterprise_id'})),
                         on = 'ue_enterprise_id', how = 'inner'
                         )

    # 合并设备信息与监测数据
    data = pd.merge(site_info,
                     (power_usage[['p_value', 'enterprise_id', 'point_id', 'code']].
                      rename(columns = {'name': 'site_name', 
                                        'p_value': 'active_power',
                                        'enterprise_id': 'ue_enterprise_id',})),
                     on = ['ue_enterprise_id', 'point_id'], how = 'inner'
                    )
    
    # 对data表，按企业、设备进行分组，计算p_value的描述性统计
    data = data.rename(columns={'p_value': 'active_power'})
    data['active_power'] = pd.to_numeric(data['active_power'], errors='coerce')
    data = data.dropna(subset=['active_power'])
    data = data[data['active_power'] > 1.0]
    cols = data.columns.tolist()
    cols.remove('active_power')
    data = data[cols + ['active_power']]
    data_desc = (data.groupby(by=cols)['active_power']
                 .describe(percentiles=[0, .25, .5, .75, 1])
                 .reset_index())
    data_desc.to_csv(f"{proj_dir}/data/raw_data/west/west_data_desc.csv", index=False)


def extract_east_data():
    """
    Extracts power usage statistics for the east region.
    """
    # Placeholder for actual extraction logic
    print("Extracting power usage statistics for the east region...")
    # Implement similar logic as in extract_west_data

    df_enterprise = pd.read_csv(f"{proj_dir}/data/raw_data/east/v_enterprise.csv").drop_duplicates()
    df_line = pd.read_csv(f"{proj_dir}/data/raw_data/east/v_production_line.csv")
    df_device = pd.read_csv(f"{proj_dir}/data/raw_data/east/v_production_line_equipment.csv")
    df_monitor_data = pd.read_csv(f"{proj_dir}/data/raw_data/east/v_monitor_data.csv")

    line_info = pd.merge(df_enterprise[['id', 'name']].rename(columns={'id': 'ue_enterprise_id',
                                                                        'name': 'ue_enterprise_name'}),
                         df_line.rename(columns={'id': 'line_id', 'name': 'line_name',
                                                 'enterprise_id': 'ue_enterprise_id'}),
                         on='ue_enterprise_id', how='inner') 

    site_info = pd.merge(line_info,
                         (df_device.drop(columns='id').
                          rename(columns={'type': 'category_code',
                                         'production_line_id': 'line_id',
                                         'equipment_mn': 'code',
                                         'equipment_name': 'facility_name'})),
                         on='line_id', how='inner')
    # 筛选出一个code对应多个line_id的数据
    df_code_to_multi_line = site_info.groupby('code').filter(lambda x: x['line_id'].nunique() > 1).reset_index(drop=True)
    df_code_to_multi_line.to_csv(f"{proj_dir}/data/raw_data/east/east_code_to_multi_line.csv", index=False)
    
    data = pd.merge(site_info,
                     (df_monitor_data[['mn', 'pt']].
                      rename(columns={'mn': 'code', 'pt': 'active_power'})),
                     on=['code'], how='inner')

    data['active_power'] = pd.to_numeric(data['active_power'], errors='coerce')
    data = data.dropna(subset=['active_power'])
    data = data[data['active_power'] > 1.0]
    cols = data.columns.tolist()
    cols.remove('active_power')
    data = data[cols + ['active_power']]
    data_desc = (data.groupby(by=cols)['active_power']
                 .describe(percentiles=[0, .25, .5, .75, 1])
                 .reset_index())
    data_desc.to_csv(f"{proj_dir}/data/raw_data/east/east_data_desc.csv", index=False)


def mask_statistics_from_db(province_pinyin: str, area_code: str, min_valid_power: float = 0.5):
    """
    Masks the statistics in the DataFrame.
    """
    # Placeholder for masking logic
    print("Masking statistics...")
    # Implement masking logic here if needed

    # ------------ Extracting info from PostgreSQL database ------------
    pre_code = replace_code_suffix(area_code)
    info_cols = ['ue_enterprise_id', 'facility_id', 'category_code', 'line_id', 
                 'ue_enterprise_name', 'line_name', 'facility_name', 'source',
                #  'facility_sequence_number', 'rated_power', 'is_gas_related'
                 ]
    
    sql_info = f"SELECT {','.join(info_cols)} FROM ue_basic_information_product_line_{province_pinyin} " \
               f"WHERE region_code LIKE '{pre_code}%'"
    df_info = query_postgre_table(database_conf, sql_info)

    # 将 None null nan - 等异常字符转为 np.nan
    df_info = df_info.map(lambda x: np.nan if x in 
                                [None, '', 'nan', '-', 'None', 'null'] 
                                else str(x))

    # 以info_cols的前四列为条件，筛选出非空的行,并以这些列为条件进行去重
    df_info = df_info[df_info[info_cols[:4]].notna().all(axis=1)]
    df_info = df_info.drop_duplicates(subset=info_cols[:4])

    # 按照ue_enterprise_id和facility_id进行分组聚合，line_id字段生成一个列表，其他info_cols中的字段取第一个不为空的值
    agg_dict = {col: (lambda x: x.dropna().iloc[0] if x.dropna().size > 0 else '') for col in info_cols[4:]}
    agg_dict.update({col: lambda x: '&|&'.join(x.dropna().unique()) for col in ['line_id', 'line_name']})
    # agg_dict.update({col: lambda x: list(x.dropna().unique()) for col in ['line_id', 'line_name']})
    df_info = df_info.groupby(info_cols[:3], as_index=False).agg(agg_dict)

    # 统计df_info中line_id列表元素有多个的行
    multi_line_info = df_info[df_info['line_id'].apply(lambda x: isinstance(x, list) and len(x) > 1)]
    multi_line_info.to_csv(f"{proj_dir}/data/raw_data/multi_line_info.csv", index=False)
    
    # -------------- Extracting data from PostgreSQL database --------------
    data_cols = ['ue_enterprise_id', 'facility_id', 'monitor_time', 'active_power', 
                #  'voltage_a', 'voltage_b', 'voltage_c'
                 ]
    sql_data = f"SELECT {','.join(data_cols)} FROM ue_basic_data_monitoring_data_{province_pinyin} " \
                f"WHERE region_code LIKE '{pre_code}%' AND time_type = '100' " \
                f"AND active_power >= {min_valid_power}"
    df_data = query_postgre_table(database_conf, sql_data)

    # 将 None null nan - 等异常字符转为 np.nan
    for col in data_cols[:2]:
        df_data[col] = df_data[col].map(lambda x: np.nan if x in 
                                        [None, '', 'nan', '-', 'None', 'null'] 
                                        else str(x))

    # 将'active_power', 'voltage_a', 'voltage_b', 'voltage_c'转为浮点型，错误的转为nan
    for col in ['active_power', 
                # 'voltage_a', 'voltage_b', 'voltage_c'
                ]:
        df_data[col] = pd.to_numeric(df_data[col], errors='coerce')

    # 将monitor_time转为datetime格式
    df_data['monitor_time'] = pd.to_datetime(df_data['monitor_time'], errors='coerce')

    # # 以data_cols的前三列为条件，筛选出非空的行,并以这些列进行分组，对其余字段取np.nanmean
    df_data = df_data[df_data[data_cols[:3]].notna().all(axis=1)]
    agg_dict = {col: (np.nanmean if col != 'source' else 'first') for col in data_cols[3:]}
    df_data = df_data.groupby(data_cols[:3], as_index=False).agg(agg_dict)

    # # 去除active_power的值小于1.0的行
    # df_data = df_data[df_data['active_power'] >= min_valid_power]
    
    # ------ 按照ue_enterprise_id和facility_id合并df_info和df_data ---------
    data = pd.merge(df_info, df_data, on=['ue_enterprise_id', 'facility_id'], how='inner')

    # 统计df_data中上述merge不成功的行
    unmatched_info = df_data[['ue_enterprise_id', 'facility_id']].drop_duplicates()
    unmatched_info = unmatched_info[(~unmatched_info.set_index(['ue_enterprise_id', 'facility_id']).index.isin(
        df_info.set_index(['ue_enterprise_id', 'facility_id']).index))]
    unmatched_info.to_csv(f"{proj_dir}/data/raw_data/unmatched_info.csv", index=False)

    # ---------- 输出有功功率的描述性统计 -----------
    for col in ['line_id', 'line_name']:
        if col in data.columns and isinstance(data[col].iloc[0], (list, tuple)):
            data[col] = data[col].apply(lambda x: '&|&'.join(x))

    cols = ['ue_enterprise_id', 'ue_enterprise_name', 'line_id', 'line_name', 
            'facility_id', 'facility_name', 'category_code', 'source']
    data = data[cols + ['active_power']]

    data_desc = (data.groupby(by=cols)['active_power']
                 .describe(percentiles=[0, .25, .5, .75, 1])
                 .reset_index())
    data_desc.to_csv(f"{proj_dir}/data/raw_data/data_desc.csv", index=False)

    print(data_desc.shape)

    return data_desc


if __name__ == "__main__":
    # extract_west_data()
    # extract_east_data()
    mask_statistics_from_db('chongqing', '500107000')
    print("Extraction completed.")
