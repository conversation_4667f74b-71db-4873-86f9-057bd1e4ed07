# -*- coding: utf-8 -*-
"""
Created on 2025/7/3 下午2:44

@Project -> File: srv-enterprise-power-usage-verify -> __init__.py.py

@Author: vincent-cj

@Describe:
"""
import logging
import time
import sys
import os
import argparse
import traceback
import asyncio
import requests
import pandas as pd
from functools import wraps, partial
from concurrent.futures import ThreadPoolExecutor, wait, ALL_COMPLETED
logging.basicConfig(level = logging.INFO)
sys.path.append('../')
from mod.config.config_loader import config_loader
from mod.config.logObj import Logger
from mod.tool.dir_file_op import load_yaml, transform_str2bool
requests.api.request = partial(requests.api.request, verify = False)

# logging configure
log_conf = config_loader.proj_config['log']
logger = Logger(log_conf['path'], log_conf['level'], log_conf['when'],
                log_conf['backCount'], log_conf['format']).logger
logging.getLogger('datashaper').setLevel(logging.ERROR)

proj_dir, proj_cmap = config_loader.proj_dir, config_loader.proj_cmap
env_conf = config_loader.environ_config
proj_conf = config_loader.proj_config


parser = argparse.ArgumentParser(description = '执法大模型末端数据校验算法环境变量')
parser.add_argument('--env-name', type = str, help = 'running environment, from `local/shutian/gonv_jlp`',
                    default = env_conf['env_name'])
parser.add_argument('--is-upload-file', type = str, help = 'whether or not upload file, default as true',
                    default = None)
args = parser.parse_args()

# -------- env cfg -------
env_name = args.env_name
if env_name == 'local':
    logger.info('当前运行环境为：本地环境')
elif env_name == 'shutian':
    logger.info('当前运行环境为：蜀天云')
elif env_name == 'govn_jlp':
    logger.info('当前运行环境为：九龙坡政务云')
else:
    logger.error('请输入正确的运行环境')
    exit()

env_cfg = env_conf[env_name]
if args.is_upload_file is not None:
    is_upload_file = transform_str2bool(args.is_upload_file)
    env_cfg['IS_UPLOAD_FILE'] = is_upload_file


database_conf = {'host': env_cfg['DB_HOST'], 'port': env_cfg['DB_PORT'],
                 'database': env_cfg['DB_NAME'], 'user': env_cfg['DB_USER'],
                 'password': env_cfg['DB_PASSWD']}

LLM_URL = env_cfg['LLM_URL']
LLM_MODEL = env_cfg['LLM_MODEL']
STOP_TOKEN_IDS = env_cfg['STOP_TOKEN_IDS']
RANDOM_SEED = env_cfg['RANDOM_SEED']

upload_file_url = env_cfg['UPLOA_FILE_URL']
is_upload_file = env_cfg['IS_UPLOAD_FILE']
num_workers = env_cfg['NUM_WORKERS']

logger.info(f'==> parser args\n{args}\n{env_cfg}')


# -------- 缓存目录 -------
cached_dir = proj_conf['cached_dir']
cached_dir = f'{proj_dir}/{cached_dir}'
os.makedirs(cached_dir, exist_ok = True)


city_code_df = pd.read_csv(f'{proj_dir}/file/dim_ref_comm_prov_city.csv',
                           keep_default_na = False,
                           dtype = str)
city_code_province_pinyin_map = dict(zip(city_code_df['city_code'], city_code_df['province_pinyin']))
city_pinyin_code_map = dict(zip(city_code_df['city_pinyin'], city_code_df['city_code']))
city_code_pinyin_map = dict(zip(city_code_df['city_code'], city_code_df['city_pinyin']))


def time_cost(func, dispaly = logger.info):
    @wraps(func)
    def _time_cost(*args, **kwargs):
        start = time.time()
        func_path = f"{sys.modules[func.__module__].__file__.replace(proj_dir, '')}/{func.__name__}"
        ret = func(*args, **kwargs)
        dispaly(f"==> time-cost: {time.time() - start:.6f}     {func_path}")
        return ret
    return _time_cost


def logging_decorator(func):
    @wraps(func)
    def _wrapper(*args, **kwargs):
        start = time.time()
        arg_0 = ''
        func_path = f"{arg_0}{sys.modules[func.__module__].__file__.replace(proj_dir, '')}/{func.__name__}"
        try:
            logger.info(f'==> excuting {func_path}')
            ret = func(*args, **kwargs)
            logger.info(f"==> time costing: {time.time() - start:.6f}    {func_path}")
            return ret
        except Exception as e:
            error_msg = f'>>>>> FAIL: `{func_path}` calculating error -> {e}'
            import traceback
            print(traceback.format_exc())
            logger.error(error_msg)
            raise ValueError(error_msg)
    return _wrapper


def logging_decorator_arg(func):
    @wraps(func)
    def _wrapper(*args, **kwargs):
        start = time.time()
        arg_0 = f'`{args[0]}` for ' if len(args) > 0 and isinstance(args[0], str) else ''
        func_path = f"{arg_0}{sys.modules[func.__module__].__file__.replace(proj_dir, '')}/{func.__name__}"
        try:
            logger.info(f'==> excuting {func_path}')
            ret = func(*args, **kwargs)
            logger.info(f"==> time costing: {time.time() - start:.6f}    {func_path}")
            return ret
        except Exception as e:
            error_msg = f'>>>>> FAIL: `{func_path}` calculating error -> {e}'
            logger.error(error_msg)
            raise ValueError(error_msg)
    return _wrapper


def llm_decorator(func):
    @wraps(func)
    def _wrapper(*args, **kwargs):
        start = time.time()
        func_path = f"{sys.modules[func.__module__].__file__.replace(proj_dir, '')}/{func.__name__}"
        logger.info(f'==> excuting {func_path}')

        e, cnt, max_retries = '', 1, 3
        while cnt <= max_retries:
            try:
                ret = func(*args, **kwargs)
                logger.info(f"==> time costing: {time.time() - start:.6f}    {func_path}")
                return ret
            except Exception as err:
                e = err
                error_msg = f'==> {cnt}th time calculating error, task retrying -> {traceback.format_exc()}'
                cnt += 1
                logger.error(error_msg)
        else:
            error_msg = f'>>>>> FAIL: `{func_path}` calculating error more than {max_retries} times, task aborted -> {e}'
            logger.error(error_msg)
            raise ValueError(error_msg)
    return _wrapper


def thread_decorator(func):
    @wraps(func)
    def _wrapper(*args, **kwargs):
        try:
            loop = asyncio.get_running_loop()
            logger.info(f'==> one asyncio loop in running, a new sub thred is created')
            with ThreadPoolExecutor(max_workers = 1) as t:
                all_task = [t.submit(func, *args, **kwargs)]
                wait(all_task, return_when = ALL_COMPLETED)
            return all_task[0].result()
        except RuntimeError:
            return func(*args, **kwargs)
    return _wrapper
