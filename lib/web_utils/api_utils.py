"""
#!/usr/bin/env python
#-*-coding: utf-8 -*-

  @ Author: vincent-cj

  @ Date: 2025-07-17 20:07:38 PM

  @ FilePath: -> srv-enterprise-power-usage-verify -> lib -> web_utils -> api_utils.py

  @ Description: 

"""
import re
import datetime


def ckeck_date_format(date):
    """校验时间格式"""
    # '2018-01-01', '2018-01-01 08', '2018-01-01 08:', '2018-01-01 08:00', '2018-01-01 08:00:00'
    matched = re.match(r'(\d{4}-\d{2}-\d{2})\s*(\d{2})?:?(\d{2})?:?(\d{2})?$', str(date).strip())
    if matched is None:
        raise ValueError(f'时间格式错误，传入时间格式为: {date}')
    
    grouped = matched.groups()
    grouped = [item if item is not None else "00" for item in grouped]
    date_str = grouped.pop(0)
    date_str += f" {':'.join(grouped)}"
    
    # Validate the time
    try:
        datetime.datetime.strptime(f"{date_str}", '%Y-%m-%d %H:%M:%S')
    except ValueError:
        raise ValueError(f'时间无效，传入时间为: {date}')
    
    return date_str

