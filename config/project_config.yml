
cached_dir: 'data/cached/'


# ============ 日志配置 ============
logging:
    version: 1
    formatters:
        simpleFormater:
            format: '%(asctime)s - %(levelname)s - %(filename)s[line:%(lineno)d]: %(message)s'
            datefmt: '%Y-%m-%d %H:%M:%S'
    handlers:
        console_handler:
            level: DEBUG
            class: logging.StreamHandler
            formatter: simpleFormater
            stream: ext://sys.stdout
        info_handler:
            level: INFO
            class: logging.FileHandler
            formatter: simpleFormater
            filename: data/logs/task.logs
            encoding: utf8
        error_handler:
            level: ERROR
            class: logging.FileHandler
            formatter: simpleFormater
            filename: data/logs/task.logs
            encoding: utf8
#        time_rotating_handler:
#            class: logging.handlers.TimedRotatingFileHandler
#            formatter: simpleFormater
#            filename: ../logs/task.logs
#            when: D
#            encoding: utf8
#            backupCount: 60
#            interval: 1
    root:
        level: INFO
        handlers: [console_handler, info_handler, error_handler]

log:
    path: data/logs/    # 日志文件保存地址
    level: info                          # 日志文件记录级别
    when: d                              # 一个日志文件的记录周期
    backCount: 10                         # 最大保留的日志文件数量
    format: '%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s'