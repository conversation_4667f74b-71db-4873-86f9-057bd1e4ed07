docker rm -f ent-ue-check
docker rmi hub-dev.rockontrol.com/rk-ai-tools/srv-enterprise-power-usage-verify:0.0.1
docker build -t hub-dev.rockontrol.com/rk-ai-tools/srv-enterprise-power-usage-verify:0.0.1 -f Dockerfile_arm .
docker run -d --privileged=True --restart=always --shm-size=16G \
  --name ent-ue-check \
  -v ./data:/workspace/data -p 6001:8080 \
  hub-dev.rockontrol.com/rk-ai-tools/srv-enterprise-power-usage-verify:0.0.1