# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese enterprise power usage verification system (企业用能数据校验) that processes and analyzes power consumption data from two regions (East/West) in Jiulongpo district. The system extracts data from CSV files and PostgreSQL databases, performs data validation, aggregation, and statistical analysis.

## Key Commands

### Environment Setup
```bash
pip install -r requirements.txt
```

### Running the Application
```bash
# Run power usage verification
python -m lib.step_0_check_power.step_0_verify_power_usage

# Run power usage extraction
python -m lib.step_0_check_power.extract_power_usage_statistics
```

### Docker Commands
```bash
# Build Docker image
docker build -t srv-enterprise-power-usage-verify .

# Run container
docker run -d -p 8080:8080 srv-enterprise-power-usage-verify
```

## Architecture

### Core Components

1. **Configuration System** (`mod/config/`)
   - `config_loader.py`: Loads YAML configurations and environment settings
   - `logObj.py`: Logging configuration and utilities
   - Configurations stored in `config/default.yml` and `config/project_config.yml`

2. **Database Layer** (`mod/database/`)
   - `query_postgre_table.py`: PostgreSQL query utilities
   - `query_mysql.py`: MySQL query utilities
   - Supports both Shu and RK database configurations

3. **Data Processing** (`lib/step_0_check_power/`)
   - `extract_power_usage_statistics.py`: Extracts power usage data from CSV files and databases
   - `step_0_verify_power_usage.py`: Main verification logic with database queries
   - `utils.py`: Helper functions for data processing

4. **Utilities** (`mod/tool/`)
   - `dir_file_op.py`: File and directory operations
   - `time_conversion.py`: Time handling utilities
   - `s3_check_md5.py`: S3 and MD5 utilities

### Data Flow

1. **Data Sources**: CSV files in `data/raw_data/` (east/ and west/ regions)
2. **Database**: PostgreSQL with configurable connections (Shu/RK databases)
3. **Processing**: Data extraction, validation, aggregation
4. **Output**: Cached results in `data/cached/`, logs in `data/logs/`

### Key Configuration

- Database configurations support both internal and external connections
- LLM integration for data processing (configurable models)
- Logging with file rotation and multiple handlers
- Environment-based configuration switching

### Dependencies

- `pandas`: Data manipulation and analysis
- `psycopg2-binary`: PostgreSQL database adapter
- `thefuzz`: Fuzzy string matching
- `cn2an`: Chinese number conversion

### Important Notes

- The system processes power usage data for enterprise monitoring
- Supports both east and west region data sources
- Includes data validation and duplicate handling
- Uses decorators for logging, timing, and error handling
- Configurable database connections and LLM integration