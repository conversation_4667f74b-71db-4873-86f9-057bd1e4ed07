# -*- coding: utf-8 -*-
"""
Created on 2021/7/29 下午4:21

@Project -> File: srv-wrfcmaq-forecast-post-huhehaote-online-encrypt -> encrypt.py

@Author: <PERSON><PERSON><PERSON>

@Describe:
"""
import os
import re
import shutil
import compileall

proj_path = os.path.abspath(os.path.dirname(__file__))


def mv_pyc(_dir):
    """将生成的pyc文件移动到上级目录，并重命名"""
    sub_list = os.listdir(_dir)
    for sub_ in sub_list:
        if os.path.isdir(f'{_dir}/{sub_}'):
            # 对于pycache目录，将内部的.pyc文件移动到其父级目录
            if sub_ == '__pycache__':
                file_list = os.listdir(f'{_dir}/{sub_}')
                for file in file_list:
                    if file.endswith('.pyc'):
                        new_name = re.sub(r'\..*python.*\.', '.', file)
                        shutil.move(f'{_dir}/{sub_}/{file}', f'{_dir}/{new_name}')
                shutil.rmtree(f'{_dir}/{sub_}')

            # 对于普通目录，递归处理
            elif not sub_.startswith('.'):
                mv_pyc(f'{_dir}/{sub_}/')

        # 删掉除encrypt.py以外的.py文件
        elif os.path.isfile(f'{_dir}/{sub_}'):
            if sub_.endswith('.py') and (sub_ != 'encrypt.py'):
                os.remove(f'{_dir}/{sub_}')


if __name__ == '__main__':
    compileall.compile_dir(proj_path)
    mv_pyc(proj_path)

