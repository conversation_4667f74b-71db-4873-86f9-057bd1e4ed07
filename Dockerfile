FROM hub-dev.rockontrol.com/rk-ai-tools/env-ml-python3-pytorch:0.0.0-ce310b6
WORKDIR /workspace
ADD . /workspace

ENV LANG=zh_CN.UTF-8

RUN apt -y update && apt -y install pandoc libreoffice && apt clean all && rm -rf /var/lib/apt/lists/* \
    && pip config --global set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    && pip config --global set global.trusted-host pypi.tuna.tsinghua.edu.cn \
    && pip3 install -r /workspace/requirements.txt -i  https://pypi.tuna.tsinghua.edu.cn/simple/ \
    && rm -f requirements.txt \
    && pip cache purge
    # todo
#     && python3 /workspace/encrypt.py

WORKDIR /workspace/bin_web

# 不显示warning信息
# ENTRYPOINT ["python3", "-W", "ignore", "web_server.pyc"]

ENTRYPOINT ["python3", "-W", "ignore", "web_server.py"]
CMD ["--env-name", "shutian"]
